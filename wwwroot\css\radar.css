/* 雷达系统样式文件 */

/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

/* 确保所有文字都是白色 */
h1, h2, h3, h4, h5, h6, p, span, div, label, a, li, td, th {
    color: #ffffff !important;
}

/* 链接样式 */
a {
    color: #00ff00 !important;
    text-decoration: none;
}

a:hover {
    color: #00cc00 !important;
    text-decoration: underline;
}

/* 卡片文字 */
.card-title, .card-text {
    color: #ffffff !important;
}

/* 按钮文字 */
.btn {
    color: #ffffff !important;
}

.btn-success {
    background-color: #00ff00 !important;
    border-color: #00ff00 !important;
    color: #000000 !important;
}

.btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}

.btn-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #000000 !important;
}

.btn-info {
    background-color: #0dcaf0 !important;
    border-color: #0dcaf0 !important;
    color: #000000 !important;
}

/* 雷达容器样式 */
.radar-container {
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    overflow: hidden;
    height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
}

.radar-header {
    background: linear-gradient(90deg, #2d2d2d 0%, #1a1a1a 100%);
    border-bottom: 2px solid #00ff00;
    min-height: 50px;
}

.radar-footer {
    background: linear-gradient(90deg, #1a1a1a 0%, #2d2d2d 100%);
    border-top: 1px solid #333;
    min-height: 40px;
}

/* 画布容器 */
#radar-canvas-container {
    flex: 1;
    background-color: #0a0a0a;
    background-image: 
        radial-gradient(circle at 25% 25%, #003300 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, #001100 0%, transparent 50%);
    overflow: hidden;
}

#radar-canvas {
    width: 100%;
    height: 100%;
    cursor: crosshair;
}

/* 加载指示器 */
#radar-loading {
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 10px;
    padding: 20px;
}

/* 信息面板样式 */
.info-panel {
    background-color: #1a1a1a;
    border-left: 1px solid #333;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

.panel-section {
    border-color: #333 !important;
}

.panel-header {
    background-color: #2d2d2d;
    border-bottom: 1px solid #333;
}

.panel-content {
    max-height: 300px;
    overflow-y: auto;
}

/* 自定义滚动条 */
.panel-content::-webkit-scrollbar,
.info-panel::-webkit-scrollbar {
    width: 6px;
}

.panel-content::-webkit-scrollbar-track,
.info-panel::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.panel-content::-webkit-scrollbar-thumb,
.info-panel::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.info-panel::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 列表项样式 */
.list-group-item-dark {
    background-color: #2d2d2d !important;
    border-color: #333 !important;
    color: #ffffff !important;
    padding: 8px 12px;
    font-size: 0.875rem;
}

.list-group-item-dark:hover {
    background-color: #3d3d3d !important;
}

/* 进度条样式 */
.progress {
    background-color: #333 !important;
}

/* 表单控件样式 */
.form-check-input:checked {
    background-color: #00ff00;
    border-color: #00ff00;
}

.form-check-input:focus {
    border-color: #00ff00;
    box-shadow: 0 0 0 0.25rem rgba(0, 255, 0, 0.25);
}

/* 按钮样式增强 */
.btn-outline-success:hover {
    background-color: #00ff00;
    border-color: #00ff00;
    color: #000000;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000000;
}

.btn-outline-info:hover {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: #000000;
}

/* 雷达控制按钮 */
.radar-controls .btn {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* 状态徽章样式 */
.badge {
    font-size: 0.7rem;
}

/* 响应式设计 */
@media (max-width: 992px) {
    .radar-container {
        height: 60vh;
    }
    
    .info-panel {
        max-height: none;
        border-left: none;
        border-top: 1px solid #333;
    }
    
    .panel-content {
        max-height: 200px;
    }
}

@media (max-width: 768px) {
    .radar-header {
        flex-direction: column;
        text-align: center;
    }
    
    .radar-controls {
        margin-top: 10px;
    }
    
    .radar-controls .btn {
        margin: 2px;
    }
    
    .radar-footer .row .col {
        font-size: 0.75rem;
    }
}

/* 全屏模式样式 */
.fullscreen-radar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: #000000;
}

.fullscreen-radar .radar-container {
    height: 100vh;
    border: none;
    border-radius: 0;
}

.fullscreen-radar .info-panel {
    position: absolute;
    top: 60px;
    right: 0;
    width: 300px;
    height: calc(100vh - 60px);
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.fullscreen-radar .info-panel.show {
    transform: translateX(0);
}

/* 雷达网格样式 */
.radar-grid {
    opacity: 0.3;
    stroke: #00ff00;
    stroke-width: 1;
}

/* 雷达元素样式类 */
.radar-player-self {
    fill: #00ff00;
    stroke: #ffffff;
    stroke-width: 2;
}

.radar-player-teammate {
    fill: #0066ff;
    stroke: #ffffff;
    stroke-width: 1;
}

.radar-player-enemy {
    fill: #ff0000;
    stroke: #ffffff;
    stroke-width: 1;
}

.radar-item-weapon {
    fill: #ffff00;
    stroke: #ffffff;
    stroke-width: 1;
}

.radar-item-vehicle {
    fill: #ff6600;
    stroke: #ffffff;
    stroke-width: 2;
}

.radar-item-extraction {
    fill: #ffffff;
    stroke: #00ff00;
    stroke-width: 2;
}

/* 动画效果 */
@keyframes radar-pulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

.radar-pulse {
    animation: radar-pulse 2s infinite;
}

@keyframes radar-scan {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.radar-scan {
    animation: radar-scan 4s linear infinite;
}

/* 工具提示样式 */
.radar-tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.75rem;
    border: 1px solid #00ff00;
    z-index: 10001;
    pointer-events: none;
    white-space: nowrap;
}

/* 设置页面样式 */
.settings-container {
    background-color: #1a1a1a;
    border-radius: 8px;
    padding: 20px;
    margin: 20px;
}

.settings-section {
    background-color: #2d2d2d;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #333;
}

.settings-section h5 {
    color: #00ff00;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #333;
}

/* 范围滑块样式 */
.form-range {
    background-color: #333;
}

.form-range::-webkit-slider-thumb {
    background-color: #00ff00;
}

.form-range::-moz-range-thumb {
    background-color: #00ff00;
    border: none;
}

/* 选择框样式 */
.form-select {
    background-color: #2d2d2d;
    border-color: #333;
    color: #ffffff;
}

.form-select:focus {
    background-color: #2d2d2d;
    border-color: #00ff00;
    color: #ffffff;
    box-shadow: 0 0 0 0.25rem rgba(0, 255, 0, 0.25);
}

.form-select option {
    background-color: #2d2d2d;
    color: #ffffff;
}

/* 输入框样式 */
.form-control {
    background-color: #2d2d2d;
    border-color: #333;
    color: #ffffff;
}

.form-control:focus {
    background-color: #2d2d2d;
    border-color: #00ff00;
    color: #ffffff;
    box-shadow: 0 0 0 0.25rem rgba(0, 255, 0, 0.25);
}

.form-control::placeholder {
    color: #888;
}

/* 错误和成功消息样式 */
.alert-success {
    background-color: rgba(0, 255, 0, 0.1);
    border-color: #00ff00;
    color: #00ff00;
}

.alert-danger {
    background-color: rgba(255, 0, 0, 0.1);
    border-color: #ff0000;
    color: #ff0000;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: #ffc107;
    color: #ffc107;
}

.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    border-color: #0dcaf0;
    color: #0dcaf0;
}
