<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - 三角洲行动雷达</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="~/css/radar.css" rel="stylesheet" />
    <style>
        body {
            background-color: #1a1a1a;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background-color: #2d2d2d !important;
            border-bottom: 1px solid #444;
        }
        .navbar-brand, .nav-link {
            color: #ffffff !important;
        }
        .nav-link:hover {
            color: #00ff00 !important;
        }
        .container-fluid {
            padding: 0;
        }
        .main-content {
            min-height: calc(100vh - 56px);
        }
        .footer {
            background-color: #2d2d2d;
            border-top: 1px solid #444;
            padding: 10px 0;
            text-align: center;
            color: #888;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-dark">
            <div class="container">
                <a class="navbar-brand" href="/">🎯 三角洲行动雷达</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">主页</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Radar" asp-action="Index">雷达</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Radar" asp-action="Fullscreen">全屏雷达</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Radar" asp-action="Settings">设置</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Radar" asp-action="Test">测试数据</a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <span class="navbar-text" id="connection-status">
                                <span class="badge bg-secondary">未连接</span>
                            </span>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    
    <div class="container-fluid main-content">
        <main role="main">
            @RenderBody()
        </main>
    </div>

    <footer class="footer">
        <div class="container">
            <span>&copy; 2025 - 三角洲行动雷达系统 - <a href="https://github.com" target="_blank">GitHub</a></span>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/8.0.0/signalr.min.js"></script>
    <script src="https://unpkg.com/konva@9/konva.min.js"></script>
    <script src="~/js/radar.js"></script>
    
    <script>
        // 全局SignalR连接状态管理
        window.radarConnection = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            const statusElement = document.getElementById('connection-status');
            
            // 初始化SignalR连接
            window.radarConnection = new signalR.HubConnectionBuilder()
                .withUrl("/radarhub")
                .withAutomaticReconnect()
                .build();
            
            // 连接状态事件
            window.radarConnection.onreconnecting(() => {
                statusElement.innerHTML = '<span class="badge bg-warning">重连中...</span>';
            });
            
            window.radarConnection.onreconnected(() => {
                statusElement.innerHTML = '<span class="badge bg-success">已连接</span>';
            });
            
            window.radarConnection.onclose(() => {
                statusElement.innerHTML = '<span class="badge bg-danger">连接断开</span>';
            });
            
            // 启动连接
            window.radarConnection.start()
                .then(() => {
                    statusElement.innerHTML = '<span class="badge bg-success">已连接</span>';
                    console.log('SignalR连接已建立');
                })
                .catch(err => {
                    statusElement.innerHTML = '<span class="badge bg-danger">连接失败</span>';
                    console.error('SignalR连接失败:', err);
                });
        });
    </script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
