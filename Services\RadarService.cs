using DeltaForce_Radar.Models;
using Microsoft.Extensions.Caching.Memory;

namespace DeltaForce_Radar.Services
{
    /// <summary>
    /// 雷达服务实现
    /// </summary>
    public class RadarService : IRadarService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<RadarService> _logger;
        private const string RADAR_DATA_KEY = "radar_data";
        private readonly object _lockObject = new object();

        public RadarService(IMemoryCache cache, ILogger<RadarService> logger)
        {
            _cache = cache;
            _logger = logger;
            
            // 初始化雷达数据
            InitializeRadarData();
        }

        private void InitializeRadarData()
        {
            var radarData = new RadarData
            {
                MapInfo = new MapInfo
                {
                    Name = "默认地图",
                    Width = 1000f,
                    Height = 1000f,
                    Scale = 1.0f
                },
                SessionId = Guid.NewGuid().ToString()
            };

            _cache.Set(RADAR_DATA_KEY, radarData, TimeSpan.FromHours(24));
            _logger.LogInformation("雷达数据已初始化");
        }

        public async Task<RadarData> GetRadarDataAsync()
        {
            return await Task.FromResult(GetRadarData());
        }

        private RadarData GetRadarData()
        {
            if (!_cache.TryGetValue(RADAR_DATA_KEY, out RadarData? radarData) || radarData == null)
            {
                InitializeRadarData();
                radarData = _cache.Get<RadarData>(RADAR_DATA_KEY)!;
            }

            return radarData;
        }

        public async Task UpdatePlayerAsync(PlayerInfo player)
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var radarData = GetRadarData();
                    var existingPlayer = radarData.Players.FirstOrDefault(p => p.Id == player.Id);
                    
                    if (existingPlayer != null)
                    {
                        // 更新现有玩家
                        existingPlayer.Position = player.Position;
                        existingPlayer.Rotation = player.Rotation;
                        existingPlayer.Health = player.Health;
                        existingPlayer.Armor = player.Armor;
                        existingPlayer.IsAlive = player.IsAlive;
                        existingPlayer.WeaponName = player.WeaponName;
                        existingPlayer.LastUpdateTime = DateTime.Now;
                    }
                    else
                    {
                        // 添加新玩家
                        player.LastUpdateTime = DateTime.Now;
                        radarData.Players.Add(player);
                    }

                    radarData.Timestamp = DateTime.Now;
                    _cache.Set(RADAR_DATA_KEY, radarData, TimeSpan.FromHours(24));
                }
            });

            _logger.LogDebug($"玩家 {player.Name} 数据已更新");
        }

        public async Task UpdateItemAsync(ItemInfo item)
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var radarData = GetRadarData();
                    var existingItem = radarData.Items.FirstOrDefault(i => i.Id == item.Id);
                    
                    if (existingItem != null)
                    {
                        // 更新现有物品
                        existingItem.Position = item.Position;
                        existingItem.IsAvailable = item.IsAvailable;
                        existingItem.Value = item.Value;
                        existingItem.Description = item.Description;
                        existingItem.ExpiryTime = item.ExpiryTime;
                    }
                    else
                    {
                        // 添加新物品
                        item.SpawnTime = DateTime.Now;
                        radarData.Items.Add(item);
                    }

                    radarData.Timestamp = DateTime.Now;
                    _cache.Set(RADAR_DATA_KEY, radarData, TimeSpan.FromHours(24));
                }
            });

            _logger.LogDebug($"物品 {item.Name} 数据已更新");
        }

        public async Task RemovePlayerAsync(string playerId)
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var radarData = GetRadarData();
                    radarData.Players.RemoveAll(p => p.Id == playerId);
                    radarData.Timestamp = DateTime.Now;
                    _cache.Set(RADAR_DATA_KEY, radarData, TimeSpan.FromHours(24));
                }
            });

            _logger.LogDebug($"玩家 {playerId} 已移除");
        }

        public async Task RemoveItemAsync(string itemId)
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var radarData = GetRadarData();
                    radarData.Items.RemoveAll(i => i.Id == itemId);
                    radarData.Timestamp = DateTime.Now;
                    _cache.Set(RADAR_DATA_KEY, radarData, TimeSpan.FromHours(24));
                }
            });

            _logger.LogDebug($"物品 {itemId} 已移除");
        }

        public async Task CleanupExpiredDataAsync()
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var radarData = GetRadarData();
                    var beforePlayers = radarData.Players.Count;
                    var beforeItems = radarData.Items.Count;
                    
                    radarData.CleanupExpiredData();
                    
                    var afterPlayers = radarData.Players.Count;
                    var afterItems = radarData.Items.Count;
                    
                    if (beforePlayers != afterPlayers || beforeItems != afterItems)
                    {
                        radarData.Timestamp = DateTime.Now;
                        _cache.Set(RADAR_DATA_KEY, radarData, TimeSpan.FromHours(24));
                        _logger.LogInformation($"清理过期数据: 玩家 {beforePlayers}->{afterPlayers}, 物品 {beforeItems}->{afterItems}");
                    }
                }
            });
        }

        public async Task<RadarData> GetRadarDataAroundPlayerAsync(string playerId, float radius)
        {
            return await Task.FromResult(GetRadarDataAroundPlayer(playerId, radius));
        }

        private RadarData GetRadarDataAroundPlayer(string playerId, float radius)
        {
            var fullData = GetRadarData();
            var player = fullData.Players.FirstOrDefault(p => p.Id == playerId);
            
            if (player == null)
                return fullData;

            var filteredData = new RadarData
            {
                MapInfo = fullData.MapInfo,
                SessionId = fullData.SessionId,
                Timestamp = fullData.Timestamp
            };

            // 过滤玩家（在半径范围内）
            filteredData.Players = fullData.Players
                .Where(p => p.Position.Distance2DTo(player.Position) <= radius)
                .ToList();

            // 过滤物品（在半径范围内）
            filteredData.Items = fullData.Items
                .Where(i => i.Position.Distance2DTo(player.Position) <= radius && i.IsAvailable && !i.IsExpired())
                .ToList();

            return filteredData;
        }

        public async Task UpdatePlayersAsync(List<PlayerInfo> players)
        {
            foreach (var player in players)
            {
                await UpdatePlayerAsync(player);
            }
        }

        public async Task UpdateItemsAsync(List<ItemInfo> items)
        {
            foreach (var item in items)
            {
                await UpdateItemAsync(item);
            }
        }

        public async Task<object> GetStatisticsAsync()
        {
            return await Task.FromResult(GetRadarData().GetStatistics());
        }

        public async Task ResetDataAsync()
        {
            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    _cache.Remove(RADAR_DATA_KEY);
                    InitializeRadarData();
                }
            });

            _logger.LogInformation("雷达数据已重置");
        }
    }
}
