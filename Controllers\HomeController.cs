using Microsoft.AspNetCore.Mvc;
using DeltaForce_Radar.Services;

namespace DeltaForce_Radar.Controllers
{
    /// <summary>
    /// 主页控制器
    /// </summary>
    public class HomeController : Controller
    {
        private readonly IRadarService _radarService;
        private readonly ILogger<HomeController> _logger;

        public HomeController(IRadarService radarService, ILogger<HomeController> logger)
        {
            _radarService = radarService;
            _logger = logger;
        }

        /// <summary>
        /// 主页
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                var statistics = await _radarService.GetStatisticsAsync();
                ViewBag.Statistics = statistics;
                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载主页时发生异常");
                ViewBag.Error = "加载数据失败";
                return View();
            }
        }

        /// <summary>
        /// 关于页面
        /// </summary>
        public IActionResult About()
        {
            return View();
        }

        /// <summary>
        /// 错误页面
        /// </summary>
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View();
        }
    }
}
