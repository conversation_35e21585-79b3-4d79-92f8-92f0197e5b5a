using DeltaForce_Radar.Models;

namespace DeltaForce_Radar.Services
{
    /// <summary>
    /// 配置服务接口
    /// </summary>
    public interface IConfigService
    {
        /// <summary>
        /// 获取雷达配置
        /// </summary>
        Task<RadarConfig> GetConfigAsync();

        /// <summary>
        /// 保存雷达配置
        /// </summary>
        Task SaveConfigAsync(RadarConfig config);

        /// <summary>
        /// 重置配置为默认值
        /// </summary>
        Task ResetConfigAsync();

        /// <summary>
        /// 获取默认配置
        /// </summary>
        RadarConfig GetDefaultConfig();
    }
}
