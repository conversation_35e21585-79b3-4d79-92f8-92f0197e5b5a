@model RadarData
@{
    ViewData["Title"] = "雷达视图";
}

<div class="container-fluid p-0">
    @if (ViewBag.Error != null)
    {
        <div class="alert alert-danger m-3" role="alert">
            <i class="fas fa-exclamation-triangle"></i> @ViewBag.Error
        </div>
    }

    @if (ViewBag.Message != null)
    {
        <div class="alert alert-success m-3" role="alert">
            <i class="fas fa-check-circle"></i> @ViewBag.Message
        </div>
    }

    <div class="row g-0">
        <!-- 雷达显示区域 -->
        <div class="col-lg-9">
            <div class="radar-container">
                <div class="radar-header d-flex justify-content-between align-items-center p-2 bg-dark border-bottom">
                    <h5 class="mb-0 text-success">📡 战场雷达</h5>
                    <div class="radar-controls">
                        <button class="btn btn-sm btn-outline-success me-2" onclick="radarManager.centerOnPlayer()">
                            <i class="fas fa-crosshairs"></i> 居中
                        </button>
                        <button class="btn btn-sm btn-outline-warning me-2" onclick="radarManager.resetZoom()">
                            <i class="fas fa-search"></i> 重置缩放
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="radarManager.toggleGrid()">
                            <i class="fas fa-th"></i> 网格
                        </button>
                    </div>
                </div>
                
                <!-- Konva.js 画布容器 -->
                <div id="radar-canvas-container" class="position-relative">
                    <div id="radar-canvas"></div>
                    
                    <!-- 加载指示器 -->
                    <div id="radar-loading" class="position-absolute top-50 start-50 translate-middle text-center">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在初始化雷达...</p>
                    </div>
                </div>

                <!-- 雷达信息栏 -->
                <div class="radar-footer bg-dark border-top p-2">
                    <div class="row g-2 text-center">
                        <div class="col">
                            <small class="text-muted">缩放: </small>
                            <span id="zoom-level" class="text-success">100%</span>
                        </div>
                        <div class="col">
                            <small class="text-muted">玩家: </small>
                            <span id="player-count" class="text-primary">0</span>
                        </div>
                        <div class="col">
                            <small class="text-muted">物品: </small>
                            <span id="item-count" class="text-warning">0</span>
                        </div>
                        <div class="col">
                            <small class="text-muted">更新: </small>
                            <span id="last-update" class="text-info">--:--:--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏信息面板 -->
        <div class="col-lg-3">
            <div class="info-panel bg-dark h-100">
                <!-- 玩家列表 -->
                <div class="panel-section border-bottom">
                    <div class="panel-header p-3">
                        <h6 class="mb-0 text-primary">👥 玩家列表</h6>
                    </div>
                    <div class="panel-content p-2">
                        <div id="player-list" class="list-group list-group-flush">
                            <div class="text-center text-muted py-3">
                                <small>暂无玩家数据</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 物品列表 -->
                <div class="panel-section border-bottom">
                    <div class="panel-header p-3">
                        <h6 class="mb-0 text-warning">📦 重要物品</h6>
                    </div>
                    <div class="panel-content p-2">
                        <div id="item-list" class="list-group list-group-flush">
                            <div class="text-center text-muted py-3">
                                <small>暂无物品数据</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速过滤 -->
                <div class="panel-section border-bottom">
                    <div class="panel-header p-3">
                        <h6 class="mb-0 text-info">🔍 快速过滤</h6>
                    </div>
                    <div class="panel-content p-3">
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-teammates" checked>
                            <label class="form-check-label text-primary" for="show-teammates">显示队友</label>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-enemies" checked>
                            <label class="form-check-label text-danger" for="show-enemies">显示敌人</label>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-weapons" checked>
                            <label class="form-check-label text-warning" for="show-weapons">显示武器</label>
                        </div>
                        <div class="form-check form-switch mb-2">
                            <input class="form-check-input" type="checkbox" id="show-vehicles" checked>
                            <label class="form-check-label text-success" for="show-vehicles">显示载具</label>
                        </div>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="show-extraction" checked>
                            <label class="form-check-label text-light" for="show-extraction">显示撤离点</label>
                        </div>
                    </div>
                </div>

                <!-- 系统状态 -->
                <div class="panel-section">
                    <div class="panel-header p-3">
                        <h6 class="mb-0 text-secondary">📊 系统状态</h6>
                    </div>
                    <div class="panel-content p-3">
                        <div class="mb-2">
                            <small class="text-muted">连接状态:</small>
                            <span id="connection-status-detail" class="badge bg-secondary ms-1">未连接</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">数据更新:</small>
                            <span id="data-update-rate" class="text-success ms-1">--</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">延迟:</small>
                            <span id="connection-latency" class="text-info ms-1">-- ms</span>
                        </div>
                        <div>
                            <small class="text-muted">会话ID:</small>
                            <span id="session-id" class="text-warning ms-1 small">--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 页面加载完成后初始化雷达
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化雷达管理器
            if (typeof RadarManager !== 'undefined') {
                window.radarManager = new RadarManager('radar-canvas');
                
                // 如果有初始数据，加载它
                @if (Model != null)
                {
                    <text>
                    const initialData = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Model));
                    window.radarManager.updateRadarData(initialData);
                    </text>
                }
                
                // 设置SignalR事件监听
                if (window.radarConnection) {
                    window.radarConnection.on("UpdateRadarData", function(data) {
                        window.radarManager.updateRadarData(data);
                        updateInfoPanels(data);
                    });
                    
                    window.radarConnection.on("PlayerUpdated", function(player) {
                        window.radarManager.updatePlayer(player);
                        updatePlayerList();
                    });
                    
                    window.radarConnection.on("ItemUpdated", function(item) {
                        window.radarManager.updateItem(item);
                        updateItemList();
                    });
                }
                
                // 设置过滤器事件
                setupFilterEvents();
                
                // 定时请求数据更新
                setInterval(function() {
                    if (window.radarConnection && window.radarConnection.state === signalR.HubConnectionState.Connected) {
                        window.radarConnection.invoke("RequestRadarData");
                    }
                }, 1000); // 每秒更新一次
            } else {
                console.error('RadarManager 未定义，请检查 radar.js 是否正确加载');
                document.getElementById('radar-loading').innerHTML = 
                    '<div class="alert alert-danger">雷达系统加载失败，请刷新页面重试</div>';
            }
        });
        
        // 设置过滤器事件
        function setupFilterEvents() {
            const filters = ['show-teammates', 'show-enemies', 'show-weapons', 'show-vehicles', 'show-extraction'];
            filters.forEach(filterId => {
                document.getElementById(filterId).addEventListener('change', function() {
                    if (window.radarManager) {
                        window.radarManager.updateFilters();
                    }
                });
            });
        }
        
        // 更新信息面板
        function updateInfoPanels(data) {
            updatePlayerList(data.players);
            updateItemList(data.items);
            updateSystemStatus(data);
        }
        
        // 更新玩家列表
        function updatePlayerList(players) {
            const playerList = document.getElementById('player-list');
            if (!players || players.length === 0) {
                playerList.innerHTML = '<div class="text-center text-muted py-3"><small>暂无玩家数据</small></div>';
                return;
            }
            
            playerList.innerHTML = players.map(player => `
                <div class="list-group-item list-group-item-dark d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-${getPlayerTypeColor(player.type)} me-2">${getPlayerTypeText(player.type)}</span>
                        <small>${player.name}</small>
                    </div>
                    <div class="text-end">
                        <div class="progress" style="width: 50px; height: 4px;">
                            <div class="progress-bar bg-${getHealthColor(player.health)}" 
                                 style="width: ${player.health}%"></div>
                        </div>
                        <small class="text-muted">${player.health}%</small>
                    </div>
                </div>
            `).join('');
        }
        
        // 更新物品列表
        function updateItemList(items) {
            const itemList = document.getElementById('item-list');
            if (!items || items.length === 0) {
                itemList.innerHTML = '<div class="text-center text-muted py-3"><small>暂无物品数据</small></div>';
                return;
            }
            
            // 只显示重要物品
            const importantItems = items.filter(item => 
                item.type === 4 || item.type === 5 || item.type === 6 // Vehicle, ExtractionPoint, HighValue
            );
            
            itemList.innerHTML = importantItems.map(item => `
                <div class="list-group-item list-group-item-dark d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-${getItemTypeColor(item.type)} me-2">${getItemTypeText(item.type)}</span>
                        <small>${item.name}</small>
                    </div>
                    <small class="text-muted">${item.value}</small>
                </div>
            `).join('');
        }
        
        // 更新系统状态
        function updateSystemStatus(data) {
            document.getElementById('player-count').textContent = data.players ? data.players.length : 0;
            document.getElementById('item-count').textContent = data.items ? data.items.length : 0;
            document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
            document.getElementById('session-id').textContent = data.sessionId || '--';
        }
        
        // 辅助函数
        function getPlayerTypeColor(type) {
            switch(type) {
                case 0: return 'success'; // Self
                case 1: return 'primary'; // Teammate
                case 2: return 'danger';  // Enemy
                default: return 'secondary';
            }
        }
        
        function getPlayerTypeText(type) {
            switch(type) {
                case 0: return '自己';
                case 1: return '队友';
                case 2: return '敌人';
                default: return '未知';
            }
        }
        
        function getHealthColor(health) {
            if (health > 75) return 'success';
            if (health > 50) return 'warning';
            if (health > 25) return 'danger';
            return 'dark';
        }
        
        function getItemTypeColor(type) {
            switch(type) {
                case 4: return 'success'; // Vehicle
                case 5: return 'light';   // ExtractionPoint
                case 6: return 'warning'; // HighValue
                default: return 'secondary';
            }
        }
        
        function getItemTypeText(type) {
            switch(type) {
                case 4: return '载具';
                case 5: return '撤离点';
                case 6: return '高价值';
                default: return '物品';
            }
        }
    </script>
}
