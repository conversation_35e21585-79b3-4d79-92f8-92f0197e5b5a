using Microsoft.AspNetCore.Mvc;
using DeltaForce_Radar.Models;
using DeltaForce_Radar.Services;

namespace DeltaForce_Radar.Controllers.Api
{
    /// <summary>
    /// 雷达数据API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class RadarDataController : ControllerBase
    {
        private readonly IRadarService _radarService;
        private readonly ILogger<RadarDataController> _logger;

        public RadarDataController(IRadarService radarService, ILogger<RadarDataController> logger)
        {
            _radarService = radarService;
            _logger = logger;
        }

        /// <summary>
        /// 获取雷达数据
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<RadarData>> GetRadarData()
        {
            try
            {
                var data = await _radarService.GetRadarDataAsync();
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取雷达数据失败");
                return StatusCode(500, new { error = "获取雷达数据失败" });
            }
        }

        /// <summary>
        /// 获取指定玩家周围的雷达数据
        /// </summary>
        [HttpGet("around/{playerId}")]
        public async Task<ActionResult<RadarData>> GetRadarDataAroundPlayer(string playerId, [FromQuery] float radius = 500f)
        {
            try
            {
                var data = await _radarService.GetRadarDataAroundPlayerAsync(playerId, radius);
                return Ok(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取玩家周围雷达数据失败");
                return StatusCode(500, new { error = "获取雷达数据失败" });
            }
        }

        /// <summary>
        /// 更新玩家信息
        /// </summary>
        [HttpPost("player")]
        public async Task<IActionResult> UpdatePlayer([FromBody] PlayerInfo player)
        {
            try
            {
                if (string.IsNullOrEmpty(player.Id))
                {
                    return BadRequest(new { error = "玩家ID不能为空" });
                }

                await _radarService.UpdatePlayerAsync(player);
                return Ok(new { message = "玩家信息更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新玩家信息失败");
                return StatusCode(500, new { error = "更新玩家信息失败" });
            }
        }

        /// <summary>
        /// 批量更新玩家信息
        /// </summary>
        [HttpPost("players")]
        public async Task<IActionResult> UpdatePlayers([FromBody] List<PlayerInfo> players)
        {
            try
            {
                if (players == null || !players.Any())
                {
                    return BadRequest(new { error = "玩家列表不能为空" });
                }

                await _radarService.UpdatePlayersAsync(players);
                return Ok(new { message = $"成功更新 {players.Count} 个玩家信息" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新玩家信息失败");
                return StatusCode(500, new { error = "批量更新玩家信息失败" });
            }
        }

        /// <summary>
        /// 更新物品信息
        /// </summary>
        [HttpPost("item")]
        public async Task<IActionResult> UpdateItem([FromBody] ItemInfo item)
        {
            try
            {
                if (string.IsNullOrEmpty(item.Id))
                {
                    return BadRequest(new { error = "物品ID不能为空" });
                }

                await _radarService.UpdateItemAsync(item);
                return Ok(new { message = "物品信息更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新物品信息失败");
                return StatusCode(500, new { error = "更新物品信息失败" });
            }
        }

        /// <summary>
        /// 批量更新物品信息
        /// </summary>
        [HttpPost("items")]
        public async Task<IActionResult> UpdateItems([FromBody] List<ItemInfo> items)
        {
            try
            {
                if (items == null || !items.Any())
                {
                    return BadRequest(new { error = "物品列表不能为空" });
                }

                await _radarService.UpdateItemsAsync(items);
                return Ok(new { message = $"成功更新 {items.Count} 个物品信息" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新物品信息失败");
                return StatusCode(500, new { error = "批量更新物品信息失败" });
            }
        }

        /// <summary>
        /// 删除玩家
        /// </summary>
        [HttpDelete("player/{playerId}")]
        public async Task<IActionResult> RemovePlayer(string playerId)
        {
            try
            {
                await _radarService.RemovePlayerAsync(playerId);
                return Ok(new { message = "玩家删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除玩家失败");
                return StatusCode(500, new { error = "删除玩家失败" });
            }
        }

        /// <summary>
        /// 删除物品
        /// </summary>
        [HttpDelete("item/{itemId}")]
        public async Task<IActionResult> RemoveItem(string itemId)
        {
            try
            {
                await _radarService.RemoveItemAsync(itemId);
                return Ok(new { message = "物品删除成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除物品失败");
                return StatusCode(500, new { error = "删除物品失败" });
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<object>> GetStatistics()
        {
            try
            {
                var stats = await _radarService.GetStatisticsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计信息失败");
                return StatusCode(500, new { error = "获取统计信息失败" });
            }
        }

        /// <summary>
        /// 清理过期数据
        /// </summary>
        [HttpPost("cleanup")]
        public async Task<IActionResult> CleanupExpiredData()
        {
            try
            {
                await _radarService.CleanupExpiredDataAsync();
                return Ok(new { message = "过期数据清理完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理过期数据失败");
                return StatusCode(500, new { error = "清理过期数据失败" });
            }
        }

        /// <summary>
        /// 重置所有数据
        /// </summary>
        [HttpPost("reset")]
        public async Task<IActionResult> ResetData()
        {
            try
            {
                await _radarService.ResetDataAsync();
                return Ok(new { message = "数据重置完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置数据失败");
                return StatusCode(500, new { error = "重置数据失败" });
            }
        }
    }
}
