@model RadarData
@{
    ViewData["Title"] = "全屏雷达";
    Layout = null; // 不使用布局，全屏显示
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - 三角洲行动雷达</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="~/css/radar.css" rel="stylesheet" />
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #000000;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }
        
        .fullscreen-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 1000;
        }
        
        .fullscreen-header {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(90deg, rgba(0,0,0,0.9) 0%, rgba(45,45,45,0.9) 100%);
            border-bottom: 2px solid #00ff00;
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }
        
        .fullscreen-radar {
            position: absolute;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
        }
        
        .fullscreen-controls {
            display: flex;
            gap: 10px;
        }
        
        .info-panel-toggle {
            position: absolute;
            top: 70px;
            right: 10px;
            z-index: 1002;
        }
        
        .fullscreen-info-panel {
            position: absolute;
            top: 60px;
            right: 0;
            width: 350px;
            height: calc(100vh - 60px);
            background-color: rgba(26, 26, 26, 0.95);
            border-left: 1px solid #333;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1001;
            overflow-y: auto;
        }
        
        .fullscreen-info-panel.show {
            transform: translateX(0);
        }
        
        .exit-fullscreen {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 1002;
        }
    </style>
</head>
<body>
    <div class="fullscreen-container">
        <!-- 退出全屏按钮 -->
        <div class="exit-fullscreen">
            <a href="@Url.Action("Index", "Radar")" class="btn btn-outline-danger btn-sm">
                <i class="fas fa-times"></i> 退出全屏
            </a>
        </div>
        
        <!-- 全屏头部 -->
        <div class="fullscreen-header">
            <div class="d-flex align-items-center">
                <h4 class="mb-0 text-success me-4">🎯 三角洲行动雷达 - 全屏模式</h4>
                <span id="connection-status-fullscreen" class="badge bg-secondary">未连接</span>
            </div>
            
            <div class="fullscreen-controls">
                <button class="btn btn-sm btn-outline-success" onclick="radarManager.centerOnPlayer()">
                    <i class="fas fa-crosshairs"></i> 居中
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="radarManager.resetZoom()">
                    <i class="fas fa-search"></i> 重置
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="radarManager.toggleGrid()">
                    <i class="fas fa-th"></i> 网格
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="toggleInfoPanel()">
                    <i class="fas fa-info-circle"></i> 信息
                </button>
            </div>
        </div>
        
        <!-- 信息面板切换按钮 -->
        <div class="info-panel-toggle">
            <button class="btn btn-outline-primary btn-sm" onclick="toggleInfoPanel()">
                <i class="fas fa-chevron-left" id="panel-toggle-icon"></i>
            </button>
        </div>
        
        <!-- 雷达显示区域 -->
        <div class="fullscreen-radar">
            <div id="fullscreen-radar-canvas" style="width: 100%; height: 100%;"></div>
            
            <!-- 加载指示器 -->
            <div id="fullscreen-radar-loading" class="position-absolute top-50 start-50 translate-middle text-center">
                <div class="spinner-border text-success" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">正在初始化全屏雷达...</p>
            </div>
        </div>
        
        <!-- 信息面板 -->
        <div id="fullscreen-info-panel" class="fullscreen-info-panel">
            <!-- 系统状态 -->
            <div class="p-3 border-bottom border-secondary">
                <h6 class="text-success mb-3">📊 系统状态</h6>
                <div class="row g-2 text-center">
                    <div class="col-6">
                        <div class="bg-dark p-2 rounded">
                            <div class="h5 text-primary mb-1" id="fs-player-count">0</div>
                            <small class="text-muted">玩家</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-dark p-2 rounded">
                            <div class="h5 text-warning mb-1" id="fs-item-count">0</div>
                            <small class="text-muted">物品</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-dark p-2 rounded">
                            <div class="h5 text-success mb-1" id="fs-zoom-level">100%</div>
                            <small class="text-muted">缩放</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-dark p-2 rounded">
                            <div class="h5 text-info mb-1" id="fs-last-update">--:--</div>
                            <small class="text-muted">更新</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 快速过滤 -->
            <div class="p-3 border-bottom border-secondary">
                <h6 class="text-info mb-3">🔍 显示过滤</h6>
                <div class="row g-2">
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="fs-show-teammates" checked>
                            <label class="form-check-label text-primary small" for="fs-show-teammates">队友</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="fs-show-enemies" checked>
                            <label class="form-check-label text-danger small" for="fs-show-enemies">敌人</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="fs-show-weapons" checked>
                            <label class="form-check-label text-warning small" for="fs-show-weapons">武器</label>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="fs-show-vehicles" checked>
                            <label class="form-check-label text-success small" for="fs-show-vehicles">载具</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 玩家列表 -->
            <div class="p-3 border-bottom border-secondary">
                <h6 class="text-primary mb-3">👥 玩家列表</h6>
                <div id="fs-player-list" style="max-height: 200px; overflow-y: auto;">
                    <div class="text-center text-muted py-3">
                        <small>暂无玩家数据</small>
                    </div>
                </div>
            </div>
            
            <!-- 重要物品 -->
            <div class="p-3">
                <h6 class="text-warning mb-3">📦 重要物品</h6>
                <div id="fs-item-list" style="max-height: 200px; overflow-y: auto;">
                    <div class="text-center text-muted py-3">
                        <small>暂无物品数据</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/8.0.0/signalr.min.js"></script>
    <script src="https://unpkg.com/konva@9/konva.min.js"></script>
    <script src="~/js/radar.js"></script>
    
    <script>
        let radarManager;
        let radarConnection;
        let infoPanelVisible = false;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initFullscreenRadar();
            initSignalR();
            setupKeyboardShortcuts();
        });
        
        // 初始化全屏雷达
        function initFullscreenRadar() {
            try {
                radarManager = new RadarManager('fullscreen-radar-canvas');
                
                // 如果有初始数据，加载它
                @if (Model != null)
                {
                    <text>
                    const initialData = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Model));
                    radarManager.updateRadarData(initialData);
                    updateFullscreenInfo(initialData);
                    </text>
                }
                
                // 隐藏加载指示器
                document.getElementById('fullscreen-radar-loading').style.display = 'none';
                
            } catch (error) {
                console.error('全屏雷达初始化失败:', error);
                document.getElementById('fullscreen-radar-loading').innerHTML = 
                    '<div class="alert alert-danger">全屏雷达初始化失败: ' + error.message + '</div>';
            }
        }
        
        // 初始化SignalR连接
        function initSignalR() {
            radarConnection = new signalR.HubConnectionBuilder()
                .withUrl("/radarhub")
                .withAutomaticReconnect()
                .build();
            
            // 连接状态事件
            const statusElement = document.getElementById('connection-status-fullscreen');
            
            radarConnection.onreconnecting(() => {
                statusElement.className = 'badge bg-warning';
                statusElement.textContent = '重连中...';
            });
            
            radarConnection.onreconnected(() => {
                statusElement.className = 'badge bg-success';
                statusElement.textContent = '已连接';
            });
            
            radarConnection.onclose(() => {
                statusElement.className = 'badge bg-danger';
                statusElement.textContent = '连接断开';
            });
            
            // 数据更新事件
            radarConnection.on("UpdateRadarData", function(data) {
                radarManager.updateRadarData(data);
                updateFullscreenInfo(data);
            });
            
            radarConnection.on("PlayerUpdated", function(player) {
                radarManager.updatePlayer(player);
            });
            
            radarConnection.on("ItemUpdated", function(item) {
                radarManager.updateItem(item);
            });
            
            // 启动连接
            radarConnection.start()
                .then(() => {
                    statusElement.className = 'badge bg-success';
                    statusElement.textContent = '已连接';
                    console.log('全屏模式SignalR连接已建立');
                    
                    // 定时请求数据更新
                    setInterval(() => {
                        if (radarConnection.state === signalR.HubConnectionState.Connected) {
                            radarConnection.invoke("RequestRadarData");
                        }
                    }, 1000);
                })
                .catch(err => {
                    statusElement.className = 'badge bg-danger';
                    statusElement.textContent = '连接失败';
                    console.error('全屏模式SignalR连接失败:', err);
                });
        }
        
        // 设置键盘快捷键
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                switch(e.key) {
                    case 'Escape':
                        window.location.href = '@Url.Action("Index", "Radar")';
                        break;
                    case 'i':
                    case 'I':
                        toggleInfoPanel();
                        break;
                    case 'c':
                    case 'C':
                        radarManager.centerOnPlayer();
                        break;
                    case 'r':
                    case 'R':
                        radarManager.resetZoom();
                        break;
                    case 'g':
                    case 'G':
                        radarManager.toggleGrid();
                        break;
                }
            });
        }
        
        // 切换信息面板
        function toggleInfoPanel() {
            const panel = document.getElementById('fullscreen-info-panel');
            const icon = document.getElementById('panel-toggle-icon');
            
            infoPanelVisible = !infoPanelVisible;
            
            if (infoPanelVisible) {
                panel.classList.add('show');
                icon.className = 'fas fa-chevron-right';
            } else {
                panel.classList.remove('show');
                icon.className = 'fas fa-chevron-left';
            }
        }
        
        // 更新全屏信息
        function updateFullscreenInfo(data) {
            if (!data) return;
            
            // 更新统计信息
            document.getElementById('fs-player-count').textContent = data.players ? data.players.length : 0;
            document.getElementById('fs-item-count').textContent = data.items ? data.items.length : 0;
            document.getElementById('fs-last-update').textContent = new Date().toLocaleTimeString().substring(0, 5);
            
            // 更新玩家列表
            updateFullscreenPlayerList(data.players);
            
            // 更新物品列表
            updateFullscreenItemList(data.items);
        }
        
        // 更新全屏玩家列表
        function updateFullscreenPlayerList(players) {
            const playerList = document.getElementById('fs-player-list');
            if (!players || players.length === 0) {
                playerList.innerHTML = '<div class="text-center text-muted py-3"><small>暂无玩家数据</small></div>';
                return;
            }
            
            playerList.innerHTML = players.map(player => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-dark rounded">
                    <div>
                        <span class="badge bg-${getPlayerTypeColor(player.type)} me-2">${getPlayerTypeText(player.type)}</span>
                        <small>${player.name}</small>
                    </div>
                    <div class="text-end">
                        <div class="progress" style="width: 40px; height: 3px;">
                            <div class="progress-bar bg-${getHealthColor(player.health)}" 
                                 style="width: ${player.health}%"></div>
                        </div>
                        <small class="text-muted">${player.health}%</small>
                    </div>
                </div>
            `).join('');
        }
        
        // 更新全屏物品列表
        function updateFullscreenItemList(items) {
            const itemList = document.getElementById('fs-item-list');
            if (!items || items.length === 0) {
                itemList.innerHTML = '<div class="text-center text-muted py-3"><small>暂无物品数据</small></div>';
                return;
            }
            
            // 只显示重要物品
            const importantItems = items.filter(item => 
                item.type === 4 || item.type === 5 || item.type === 6
            );
            
            itemList.innerHTML = importantItems.map(item => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-dark rounded">
                    <div>
                        <span class="badge bg-${getItemTypeColor(item.type)} me-2">${getItemTypeText(item.type)}</span>
                        <small>${item.name}</small>
                    </div>
                    <small class="text-muted">${item.value}</small>
                </div>
            `).join('');
        }
        
        // 辅助函数（与普通雷达页面相同）
        function getPlayerTypeColor(type) {
            switch(type) {
                case 0: return 'success';
                case 1: return 'primary';
                case 2: return 'danger';
                default: return 'secondary';
            }
        }
        
        function getPlayerTypeText(type) {
            switch(type) {
                case 0: return '自己';
                case 1: return '队友';
                case 2: return '敌人';
                default: return '未知';
            }
        }
        
        function getHealthColor(health) {
            if (health > 75) return 'success';
            if (health > 50) return 'warning';
            if (health > 25) return 'danger';
            return 'dark';
        }
        
        function getItemTypeColor(type) {
            switch(type) {
                case 4: return 'success';
                case 5: return 'light';
                case 6: return 'warning';
                default: return 'secondary';
            }
        }
        
        function getItemTypeText(type) {
            switch(type) {
                case 4: return '载具';
                case 5: return '撤离点';
                case 6: return '高价值';
                default: return '物品';
            }
        }
        
        // 窗口大小变化处理
        window.addEventListener('resize', function() {
            if (radarManager) {
                radarManager.resize();
            }
        });
    </script>
</body>
</html>
