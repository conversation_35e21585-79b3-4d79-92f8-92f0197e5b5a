using DeltaForce_Radar.Models;

namespace DeltaForce_Radar.Services
{
    /// <summary>
    /// 雷达服务接口
    /// </summary>
    public interface IRadarService
    {
        /// <summary>
        /// 获取当前雷达数据
        /// </summary>
        Task<RadarData> GetRadarDataAsync();

        /// <summary>
        /// 更新玩家信息
        /// </summary>
        Task UpdatePlayerAsync(PlayerInfo player);

        /// <summary>
        /// 更新物品信息
        /// </summary>
        Task UpdateItemAsync(ItemInfo item);

        /// <summary>
        /// 移除玩家
        /// </summary>
        Task RemovePlayerAsync(string playerId);

        /// <summary>
        /// 移除物品
        /// </summary>
        Task RemoveItemAsync(string itemId);

        /// <summary>
        /// 清理过期数据
        /// </summary>
        Task CleanupExpiredDataAsync();

        /// <summary>
        /// 获取指定玩家周围的数据
        /// </summary>
        Task<RadarData> GetRadarDataAroundPlayerAsync(string playerId, float radius);

        /// <summary>
        /// 批量更新玩家数据
        /// </summary>
        Task UpdatePlayersAsync(List<PlayerInfo> players);

        /// <summary>
        /// 批量更新物品数据
        /// </summary>
        Task UpdateItemsAsync(List<ItemInfo> items);

        /// <summary>
        /// 获取数据统计信息
        /// </summary>
        Task<object> GetStatisticsAsync();

        /// <summary>
        /// 重置所有数据
        /// </summary>
        Task ResetDataAsync();
    }
}
