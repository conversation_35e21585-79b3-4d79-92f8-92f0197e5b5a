using DeltaForce_Radar.Models;
using System.Text.Json;

namespace DeltaForce_Radar.Services
{
    /// <summary>
    /// 配置服务实现
    /// </summary>
    public class ConfigService : IConfigService
    {
        private readonly string _configFilePath;
        private readonly ILogger<ConfigService> _logger;
        private readonly object _lockObject = new object();

        public ConfigService(ILogger<ConfigService> logger)
        {
            _logger = logger;
            _configFilePath = Path.Combine("Config", "radar-config.json");
            
            // 确保配置目录存在
            var configDir = Path.GetDirectoryName(_configFilePath);
            if (!string.IsNullOrEmpty(configDir) && !Directory.Exists(configDir))
            {
                Directory.CreateDirectory(configDir);
            }
        }

        public async Task<RadarConfig> GetConfigAsync()
        {
            return await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    try
                    {
                        if (!File.Exists(_configFilePath))
                        {
                            _logger.LogInformation("配置文件不存在，创建默认配置");
                            var defaultConfig = GetDefaultConfig();
                            SaveConfigSync(defaultConfig);
                            return defaultConfig;
                        }

                        var json = File.ReadAllText(_configFilePath);
                        var config = JsonSerializer.Deserialize<RadarConfig>(json);
                        
                        if (config == null)
                        {
                            _logger.LogWarning("配置文件解析失败，使用默认配置");
                            return GetDefaultConfig();
                        }

                        return config;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "读取配置文件失败，使用默认配置");
                        return GetDefaultConfig();
                    }
                }
            });
        }

        public async Task SaveConfigAsync(RadarConfig config)
        {
            await Task.Run(() => SaveConfigSync(config));
        }

        private void SaveConfigSync(RadarConfig config)
        {
            lock (_lockObject)
            {
                try
                {
                    var options = new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };

                    var json = JsonSerializer.Serialize(config, options);
                    File.WriteAllText(_configFilePath, json);
                    
                    _logger.LogInformation("配置文件保存成功");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "保存配置文件失败");
                    throw;
                }
            }
        }

        public async Task ResetConfigAsync()
        {
            var defaultConfig = GetDefaultConfig();
            await SaveConfigAsync(defaultConfig);
            _logger.LogInformation("配置已重置为默认值");
        }

        public RadarConfig GetDefaultConfig()
        {
            return new RadarConfig
            {
                Display = new DisplaySettings
                {
                    RadarSize = 400,
                    Opacity = 0.9f,
                    ShowPlayerNames = true,
                    ShowPlayerHealth = true,
                    ShowDistances = false,
                    ShowGrid = true,
                    Theme = "dark"
                },
                Filters = new FilterSettings
                {
                    ShowTeammates = true,
                    ShowEnemies = true,
                    ShowWeapons = true,
                    ShowAmmo = false,
                    ShowMedical = true,
                    ShowEquipment = false,
                    ShowVehicles = true,
                    ShowExtractionPoints = true,
                    ShowHighValueItems = true,
                    MaxDisplayDistance = 500f
                },
                Updates = new UpdateSettings
                {
                    RefreshRate = 60,
                    DataRetentionTime = 5000,
                    AutoCleanup = true
                },
                Map = new MapSettings
                {
                    DefaultZoom = 1.0f,
                    MinZoom = 0.5f,
                    MaxZoom = 3.0f,
                    EnablePanning = true,
                    CenterOnPlayer = true,
                    DefaultMap = "default"
                }
            };
        }
    }
}
