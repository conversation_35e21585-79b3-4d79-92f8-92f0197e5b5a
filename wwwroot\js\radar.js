/**
 * 雷达管理器 - 基于Konva.js的2D雷达显示系统
 */
class RadarManager {
    constructor(containerId) {
        this.containerId = containerId;
        this.stage = null;
        this.layer = null;
        this.gridLayer = null;
        this.playerLayer = null;
        this.itemLayer = null;
        
        // 雷达配置
        this.config = {
            width: 800,
            height: 600,
            scale: 1.0,
            centerX: 0,
            centerY: 0,
            gridSize: 50,
            showGrid: true,
            maxDistance: 500
        };
        
        // 数据存储
        this.players = new Map();
        this.items = new Map();
        this.selfPlayer = null;
        
        // 颜色配置
        this.colors = {
            self: '#00ff00',
            teammate: '#0066ff',
            enemy: '#ff0000',
            weapon: '#ffff00',
            ammo: '#ff9900',
            medical: '#ff66ff',
            equipment: '#66ffff',
            vehicle: '#ff6600',
            extraction: '#ffffff',
            highValue: '#ffd700',
            grid: '#003300',
            background: '#0a0a0a'
        };
        
        this.init();
    }
    
    /**
     * 初始化雷达系统
     */
    init() {
        try {
            const container = document.getElementById(this.containerId);
            if (!container) {
                throw new Error(`容器 ${this.containerId} 未找到`);
            }
            
            // 获取容器尺寸
            const rect = container.getBoundingClientRect();
            this.config.width = rect.width || 800;
            this.config.height = rect.height || 600;
            
            // 创建Konva舞台
            this.stage = new Konva.Stage({
                container: this.containerId,
                width: this.config.width,
                height: this.config.height,
                draggable: true
            });
            
            // 创建图层
            this.gridLayer = new Konva.Layer();
            this.layer = new Konva.Layer();
            this.playerLayer = new Konva.Layer();
            this.itemLayer = new Konva.Layer();
            
            // 添加图层到舞台
            this.stage.add(this.gridLayer);
            this.stage.add(this.layer);
            this.stage.add(this.itemLayer);
            this.stage.add(this.playerLayer);
            
            // 绘制网格
            this.drawGrid();
            
            // 设置事件监听
            this.setupEvents();
            
            // 隐藏加载指示器
            const loading = document.getElementById('radar-loading');
            if (loading) {
                loading.style.display = 'none';
            }
            
            console.log('雷达系统初始化完成');
            
        } catch (error) {
            console.error('雷达系统初始化失败:', error);
            this.showError('雷达系统初始化失败: ' + error.message);
        }
    }
    
    /**
     * 设置事件监听
     */
    setupEvents() {
        // 鼠标滚轮缩放
        this.stage.on('wheel', (e) => {
            e.evt.preventDefault();
            
            const oldScale = this.stage.scaleX();
            const pointer = this.stage.getPointerPosition();
            
            const mousePointTo = {
                x: (pointer.x - this.stage.x()) / oldScale,
                y: (pointer.y - this.stage.y()) / oldScale,
            };
            
            const direction = e.evt.deltaY > 0 ? -1 : 1;
            const factor = 1.1;
            const newScale = direction > 0 ? oldScale * factor : oldScale / factor;
            
            // 限制缩放范围
            const clampedScale = Math.max(0.5, Math.min(3.0, newScale));
            
            this.stage.scale({ x: clampedScale, y: clampedScale });
            
            const newPos = {
                x: pointer.x - mousePointTo.x * clampedScale,
                y: pointer.y - mousePointTo.y * clampedScale,
            };
            
            this.stage.position(newPos);
            this.stage.batchDraw();
            
            // 更新缩放显示
            this.updateZoomDisplay(clampedScale);
        });
        
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.resize();
        });
    }
    
    /**
     * 绘制网格
     */
    drawGrid() {
        if (!this.config.showGrid) return;
        
        this.gridLayer.destroyChildren();
        
        const gridSize = this.config.gridSize;
        const width = this.config.width * 4; // 扩大网格范围
        const height = this.config.height * 4;
        
        // 垂直线
        for (let i = -width; i < width; i += gridSize) {
            const line = new Konva.Line({
                points: [i, -height, i, height],
                stroke: this.colors.grid,
                strokeWidth: 1,
                opacity: 0.3
            });
            this.gridLayer.add(line);
        }
        
        // 水平线
        for (let i = -height; i < height; i += gridSize) {
            const line = new Konva.Line({
                points: [-width, i, width, i],
                stroke: this.colors.grid,
                strokeWidth: 1,
                opacity: 0.3
            });
            this.gridLayer.add(line);
        }
        
        // 中心十字线
        const centerX = new Konva.Line({
            points: [-width, 0, width, 0],
            stroke: this.colors.grid,
            strokeWidth: 2,
            opacity: 0.6
        });
        
        const centerY = new Konva.Line({
            points: [0, -height, 0, height],
            stroke: this.colors.grid,
            strokeWidth: 2,
            opacity: 0.6
        });
        
        this.gridLayer.add(centerX);
        this.gridLayer.add(centerY);
        this.gridLayer.batchDraw();
    }
    
    /**
     * 更新雷达数据
     */
    updateRadarData(data) {
        try {
            if (!data) return;
            
            // 更新玩家数据
            if (data.players) {
                this.updatePlayers(data.players);
            }
            
            // 更新物品数据
            if (data.items) {
                this.updateItems(data.items);
            }
            
            // 更新地图信息
            if (data.mapInfo) {
                this.updateMapInfo(data.mapInfo);
            }
            
            // 重绘所有图层
            this.redrawAll();
            
        } catch (error) {
            console.error('更新雷达数据失败:', error);
        }
    }
    
    /**
     * 更新玩家数据
     */
    updatePlayers(players) {
        // 清除现有玩家
        this.players.clear();
        this.playerLayer.destroyChildren();
        
        players.forEach(player => {
            this.players.set(player.id, player);
            
            // 记录自己的位置
            if (player.type === 0) { // Self
                this.selfPlayer = player;
            }
            
            this.drawPlayer(player);
        });
    }
    
    /**
     * 绘制玩家
     */
    drawPlayer(player) {
        const pos = this.worldToScreen(player.position.x, player.position.z);
        
        // 获取玩家颜色
        let color = this.colors.enemy;
        switch (player.type) {
            case 0: color = this.colors.self; break;
            case 1: color = this.colors.teammate; break;
            case 2: color = this.colors.enemy; break;
        }
        
        // 创建玩家圆圈
        const circle = new Konva.Circle({
            x: pos.x,
            y: pos.y,
            radius: player.type === 0 ? 8 : 6,
            fill: color,
            stroke: '#ffffff',
            strokeWidth: player.type === 0 ? 2 : 1,
            opacity: player.health > 0 ? 1.0 : 0.5
        });
        
        // 添加脉冲动画（仅自己）
        if (player.type === 0) {
            const anim = new Konva.Animation((frame) => {
                const scale = 1 + Math.sin(frame.time * 0.005) * 0.2;
                circle.scale({ x: scale, y: scale });
            }, this.playerLayer);
            anim.start();
        }
        
        // 创建玩家名称
        if (player.name) {
            const text = new Konva.Text({
                x: pos.x - 20,
                y: pos.y - 25,
                text: player.name,
                fontSize: 10,
                fontFamily: 'Arial',
                fill: '#ffffff',
                align: 'center',
                width: 40
            });
            this.playerLayer.add(text);
        }
        
        // 创建生命值条
        if (player.health < 100) {
            const healthBg = new Konva.Rect({
                x: pos.x - 15,
                y: pos.y + 12,
                width: 30,
                height: 4,
                fill: '#333333',
                stroke: '#ffffff',
                strokeWidth: 1
            });
            
            const healthBar = new Konva.Rect({
                x: pos.x - 14,
                y: pos.y + 13,
                width: 28 * (player.health / 100),
                height: 2,
                fill: player.health > 50 ? '#00ff00' : player.health > 25 ? '#ffff00' : '#ff0000'
            });
            
            this.playerLayer.add(healthBg);
            this.playerLayer.add(healthBar);
        }
        
        // 添加工具提示
        this.addTooltip(circle, this.getPlayerTooltip(player));
        
        this.playerLayer.add(circle);
    }
    
    /**
     * 更新物品数据
     */
    updateItems(items) {
        // 清除现有物品
        this.items.clear();
        this.itemLayer.destroyChildren();
        
        items.forEach(item => {
            this.items.set(item.id, item);
            this.drawItem(item);
        });
    }
    
    /**
     * 绘制物品
     */
    drawItem(item) {
        const pos = this.worldToScreen(item.position.x, item.position.z);
        
        // 获取物品颜色和形状
        let color = this.colors.weapon;
        let shape = 'rect';
        
        switch (item.type) {
            case 0: color = this.colors.weapon; shape = 'rect'; break;
            case 1: color = this.colors.ammo; shape = 'circle'; break;
            case 2: color = this.colors.medical; shape = 'circle'; break;
            case 3: color = this.colors.equipment; shape = 'rect'; break;
            case 4: color = this.colors.vehicle; shape = 'rect'; break;
            case 5: color = this.colors.extraction; shape = 'star'; break;
            case 6: color = this.colors.highValue; shape = 'diamond'; break;
        }
        
        let itemShape;
        
        if (shape === 'circle') {
            itemShape = new Konva.Circle({
                x: pos.x,
                y: pos.y,
                radius: 4,
                fill: color,
                stroke: '#ffffff',
                strokeWidth: 1
            });
        } else if (shape === 'star') {
            itemShape = new Konva.Star({
                x: pos.x,
                y: pos.y,
                numPoints: 5,
                innerRadius: 3,
                outerRadius: 6,
                fill: color,
                stroke: '#ffffff',
                strokeWidth: 1
            });
        } else if (shape === 'diamond') {
            itemShape = new Konva.RegularPolygon({
                x: pos.x,
                y: pos.y,
                sides: 4,
                radius: 5,
                fill: color,
                stroke: '#ffffff',
                strokeWidth: 1,
                rotation: 45
            });
        } else {
            itemShape = new Konva.Rect({
                x: pos.x - 3,
                y: pos.y - 3,
                width: 6,
                height: 6,
                fill: color,
                stroke: '#ffffff',
                strokeWidth: 1
            });
        }
        
        // 添加工具提示
        this.addTooltip(itemShape, this.getItemTooltip(item));
        
        this.itemLayer.add(itemShape);
    }
    
    /**
     * 世界坐标转屏幕坐标
     */
    worldToScreen(worldX, worldZ) {
        const centerX = this.config.width / 2;
        const centerY = this.config.height / 2;
        
        // 如果有自己的位置，以自己为中心
        if (this.selfPlayer) {
            const offsetX = worldX - this.selfPlayer.position.x;
            const offsetZ = worldZ - this.selfPlayer.position.z;
            
            return {
                x: centerX + offsetX * 0.5, // 缩放因子
                y: centerY + offsetZ * 0.5
            };
        }
        
        return {
            x: centerX + worldX * 0.5,
            y: centerY + worldZ * 0.5
        };
    }
    
    /**
     * 添加工具提示
     */
    addTooltip(shape, text) {
        shape.on('mouseenter', () => {
            document.body.style.cursor = 'pointer';
            this.showTooltip(text);
        });
        
        shape.on('mouseleave', () => {
            document.body.style.cursor = 'default';
            this.hideTooltip();
        });
        
        shape.on('mousemove', (e) => {
            this.updateTooltipPosition(e.evt.clientX, e.evt.clientY);
        });
    }
    
    /**
     * 显示工具提示
     */
    showTooltip(text) {
        let tooltip = document.getElementById('radar-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'radar-tooltip';
            tooltip.className = 'radar-tooltip';
            document.body.appendChild(tooltip);
        }
        
        tooltip.innerHTML = text;
        tooltip.style.display = 'block';
    }
    
    /**
     * 隐藏工具提示
     */
    hideTooltip() {
        const tooltip = document.getElementById('radar-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }
    
    /**
     * 更新工具提示位置
     */
    updateTooltipPosition(x, y) {
        const tooltip = document.getElementById('radar-tooltip');
        if (tooltip) {
            tooltip.style.left = (x + 10) + 'px';
            tooltip.style.top = (y - 30) + 'px';
        }
    }
    
    /**
     * 获取玩家工具提示文本
     */
    getPlayerTooltip(player) {
        const typeText = ['自己', '队友', '敌人'][player.type] || '未知';
        return `
            <strong>${player.name}</strong><br>
            类型: ${typeText}<br>
            生命值: ${player.health}%<br>
            护甲: ${player.armor}%<br>
            武器: ${player.weapon || '无'}
        `;
    }
    
    /**
     * 获取物品工具提示文本
     */
    getItemTooltip(item) {
        const typeText = ['武器', '弹药', '医疗', '装备', '载具', '撤离点', '高价值'][item.type] || '未知';
        return `
            <strong>${item.name}</strong><br>
            类型: ${typeText}<br>
            价值: ${item.value || '未知'}
        `;
    }
    
    /**
     * 重绘所有图层
     */
    redrawAll() {
        this.gridLayer.batchDraw();
        this.layer.batchDraw();
        this.itemLayer.batchDraw();
        this.playerLayer.batchDraw();
    }
    
    /**
     * 居中到玩家位置
     */
    centerOnPlayer() {
        if (this.selfPlayer) {
            const centerX = this.config.width / 2;
            const centerY = this.config.height / 2;
            
            this.stage.position({
                x: centerX,
                y: centerY
            });
            
            this.stage.batchDraw();
        }
    }
    
    /**
     * 重置缩放
     */
    resetZoom() {
        this.stage.scale({ x: 1, y: 1 });
        this.centerOnPlayer();
        this.updateZoomDisplay(1.0);
    }
    
    /**
     * 切换网格显示
     */
    toggleGrid() {
        this.config.showGrid = !this.config.showGrid;
        if (this.config.showGrid) {
            this.drawGrid();
        } else {
            this.gridLayer.destroyChildren();
            this.gridLayer.batchDraw();
        }
    }
    
    /**
     * 更新缩放显示
     */
    updateZoomDisplay(scale) {
        const zoomElement = document.getElementById('zoom-level');
        if (zoomElement) {
            zoomElement.textContent = Math.round(scale * 100) + '%';
        }
    }
    
    /**
     * 调整大小
     */
    resize() {
        const container = document.getElementById(this.containerId);
        if (container && this.stage) {
            const rect = container.getBoundingClientRect();
            this.config.width = rect.width;
            this.config.height = rect.height;
            
            this.stage.width(this.config.width);
            this.stage.height(this.config.height);
            this.stage.batchDraw();
        }
    }
    
    /**
     * 显示错误信息
     */
    showError(message) {
        const loading = document.getElementById('radar-loading');
        if (loading) {
            loading.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> ${message}
                </div>
            `;
        }
    }
    
    /**
     * 更新过滤器
     */
    updateFilters() {
        // 重新绘制所有元素，应用过滤器
        this.redrawAll();
    }
    
    /**
     * 更新地图信息
     */
    updateMapInfo(mapInfo) {
        // 更新地图相关信息
        console.log('地图信息更新:', mapInfo);
    }
    
    /**
     * 销毁雷达系统
     */
    destroy() {
        if (this.stage) {
            this.stage.destroy();
        }
        this.hideTooltip();
    }
}
