using Microsoft.AspNetCore.Mvc;
using DeltaForce_Radar.Models;
using DeltaForce_Radar.Services;

namespace DeltaForce_Radar.Controllers.Api
{
    /// <summary>
    /// 配置管理API控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ConfigController : ControllerBase
    {
        private readonly IConfigService _configService;
        private readonly ILogger<ConfigController> _logger;

        public ConfigController(IConfigService configService, ILogger<ConfigController> logger)
        {
            _configService = configService;
            _logger = logger;
        }

        /// <summary>
        /// 获取雷达配置
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<RadarConfig>> GetConfig()
        {
            try
            {
                var config = await _configService.GetConfigAsync();
                return Ok(config);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取配置失败");
                return StatusCode(500, new { error = "获取配置失败" });
            }
        }

        /// <summary>
        /// 保存雷达配置
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> SaveConfig([FromBody] RadarConfig config)
        {
            try
            {
                if (config == null)
                {
                    return BadRequest(new { error = "配置数据不能为空" });
                }

                await _configService.SaveConfigAsync(config);
                return Ok(new { message = "配置保存成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存配置失败");
                return StatusCode(500, new { error = "保存配置失败" });
            }
        }

        /// <summary>
        /// 重置配置为默认值
        /// </summary>
        [HttpPost("reset")]
        public async Task<IActionResult> ResetConfig()
        {
            try
            {
                await _configService.ResetConfigAsync();
                return Ok(new { message = "配置重置成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置配置失败");
                return StatusCode(500, new { error = "重置配置失败" });
            }
        }

        /// <summary>
        /// 获取默认配置
        /// </summary>
        [HttpGet("default")]
        public ActionResult<RadarConfig> GetDefaultConfig()
        {
            try
            {
                var defaultConfig = _configService.GetDefaultConfig();
                return Ok(defaultConfig);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取默认配置失败");
                return StatusCode(500, new { error = "获取默认配置失败" });
            }
        }

        /// <summary>
        /// 更新显示设置
        /// </summary>
        [HttpPatch("display")]
        public async Task<IActionResult> UpdateDisplaySettings([FromBody] DisplaySettings displaySettings)
        {
            try
            {
                var config = await _configService.GetConfigAsync();
                config.Display = displaySettings;
                await _configService.SaveConfigAsync(config);
                
                return Ok(new { message = "显示设置更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新显示设置失败");
                return StatusCode(500, new { error = "更新显示设置失败" });
            }
        }

        /// <summary>
        /// 更新过滤设置
        /// </summary>
        [HttpPatch("filters")]
        public async Task<IActionResult> UpdateFilterSettings([FromBody] FilterSettings filterSettings)
        {
            try
            {
                var config = await _configService.GetConfigAsync();
                config.Filters = filterSettings;
                await _configService.SaveConfigAsync(config);
                
                return Ok(new { message = "过滤设置更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新过滤设置失败");
                return StatusCode(500, new { error = "更新过滤设置失败" });
            }
        }

        /// <summary>
        /// 更新更新设置
        /// </summary>
        [HttpPatch("updates")]
        public async Task<IActionResult> UpdateUpdateSettings([FromBody] UpdateSettings updateSettings)
        {
            try
            {
                var config = await _configService.GetConfigAsync();
                config.Updates = updateSettings;
                await _configService.SaveConfigAsync(config);
                
                return Ok(new { message = "更新设置更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新更新设置失败");
                return StatusCode(500, new { error = "更新更新设置失败" });
            }
        }

        /// <summary>
        /// 更新地图设置
        /// </summary>
        [HttpPatch("map")]
        public async Task<IActionResult> UpdateMapSettings([FromBody] MapSettings mapSettings)
        {
            try
            {
                var config = await _configService.GetConfigAsync();
                config.Map = mapSettings;
                await _configService.SaveConfigAsync(config);
                
                return Ok(new { message = "地图设置更新成功" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新地图设置失败");
                return StatusCode(500, new { error = "更新地图设置失败" });
            }
        }
    }
}
