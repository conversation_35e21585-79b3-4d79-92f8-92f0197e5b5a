@{
    ViewData["Title"] = "主页";
}

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h1 class="display-4 text-success">🎯 三角洲行动雷达系统</h1>
                <p class="lead">实时战场态势感知与目标追踪系统</p>
            </div>
        </div>
    </div>

    @if (ViewBag.Error != null)
    {
        <div class="row">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> @ViewBag.Error
                </div>
            </div>
        </div>
    }

    <div class="row g-4">
        <!-- 快速访问卡片 -->
        <div class="col-md-6 col-lg-3">
            <div class="card bg-dark border-success h-100">
                <div class="card-body text-center">
                    <div class="display-6 text-success mb-3">📡</div>
                    <h5 class="card-title">雷达视图</h5>
                    <p class="card-text">查看实时战场雷达</p>
                    <a href="@Url.Action("Index", "Radar")" class="btn btn-success">进入雷达</a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3">
            <div class="card bg-dark border-primary h-100">
                <div class="card-body text-center">
                    <div class="display-6 text-primary mb-3">🖥️</div>
                    <h5 class="card-title">全屏模式</h5>
                    <p class="card-text">全屏雷达显示</p>
                    <a href="@Url.Action("Fullscreen", "Radar")" class="btn btn-primary">全屏雷达</a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3">
            <div class="card bg-dark border-warning h-100">
                <div class="card-body text-center">
                    <div class="display-6 text-warning mb-3">⚙️</div>
                    <h5 class="card-title">系统设置</h5>
                    <p class="card-text">配置雷达参数</p>
                    <a href="@Url.Action("Settings", "Radar")" class="btn btn-warning">打开设置</a>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3">
            <div class="card bg-dark border-info h-100">
                <div class="card-body text-center">
                    <div class="display-6 text-info mb-3">🧪</div>
                    <h5 class="card-title">测试数据</h5>
                    <p class="card-text">生成演示数据</p>
                    <a href="@Url.Action("Test", "Radar")" class="btn btn-info">生成测试</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统状态 -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-center mb-4">📊 系统状态</h3>
        </div>
    </div>

    <div class="row g-4">
        @if (ViewBag.Statistics != null)
        {
            var stats = ViewBag.Statistics as dynamic;
            
            <div class="col-md-3">
                <div class="card bg-dark border-light">
                    <div class="card-body text-center">
                        <h2 class="text-success">@(stats?.TotalPlayers ?? 0)</h2>
                        <p class="card-text">总玩家数</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card bg-dark border-light">
                    <div class="card-body text-center">
                        <h2 class="text-primary">@(stats?.Teammates ?? 0)</h2>
                        <p class="card-text">队友数量</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card bg-dark border-light">
                    <div class="card-body text-center">
                        <h2 class="text-danger">@(stats?.Enemies ?? 0)</h2>
                        <p class="card-text">敌人数量</p>
                    </div>
                </div>
            </div>

            <div class="col-md-3">
                <div class="card bg-dark border-light">
                    <div class="card-body text-center">
                        <h2 class="text-warning">@(stats?.TotalItems ?? 0)</h2>
                        <p class="card-text">物品数量</p>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="col-12">
                <div class="card bg-dark border-light">
                    <div class="card-body text-center">
                        <p class="text-muted">正在加载系统状态...</p>
                    </div>
                </div>
            </div>
        }
    </div>

    <!-- 功能说明 -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="text-center mb-4">📖 功能说明</h3>
        </div>
    </div>

    <div class="row g-4">
        <div class="col-md-6">
            <div class="card bg-dark border-light h-100">
                <div class="card-body">
                    <h5 class="card-title text-success">🎯 实时追踪</h5>
                    <ul class="list-unstyled">
                        <li>• 玩家位置实时更新</li>
                        <li>• 敌友识别标记</li>
                        <li>• 生命值状态显示</li>
                        <li>• 武器装备信息</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card bg-dark border-light h-100">
                <div class="card-body">
                    <h5 class="card-title text-primary">🗺️ 地图功能</h5>
                    <ul class="list-unstyled">
                        <li>• 可缩放地图视图</li>
                        <li>• 物品位置标记</li>
                        <li>• 撤离点显示</li>
                        <li>• 载具位置追踪</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 版本信息 -->
    <div class="row mt-5">
        <div class="col-12 text-center">
            <hr class="border-secondary">
            <p class="text-muted">
                <small>
                    版本 1.0.0 | 基于 .NET 9.0 + SignalR + Konva.js 构建<br>
                    最后更新: @DateTime.Now.ToString("yyyy-MM-dd HH:mm")
                </small>
            </p>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 定时刷新统计信息
        setInterval(async function() {
            try {
                const response = await fetch('/api/radardata/statistics');
                if (response.ok) {
                    const stats = await response.json();
                    // 更新统计数据显示
                    console.log('统计信息已更新:', stats);
                }
            } catch (error) {
                console.error('获取统计信息失败:', error);
            }
        }, 30000); // 每30秒更新一次
    </script>
}
