using DeltaForce_Radar.Hubs;
using DeltaForce_Radar.Services;

var builder = WebApplication.CreateBuilder(args);

// 添加服务到容器
builder.Services.AddControllersWithViews();
builder.Services.AddSignalR();
builder.Services.AddMemoryCache();

// 注册自定义服务
builder.Services.AddScoped<IRadarService, RadarService>();
builder.Services.AddScoped<IConfigService, ConfigService>();

// 添加CORS支持
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 添加日志配置
builder.Logging.ClearProviders();
builder.Logging.AddConsole();
builder.Logging.AddDebug();

var app = builder.Build();

// 配置HTTP请求管道
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();
app.UseCors("AllowAll");

app.UseAuthorization();

// 配置MVC路由
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// 配置API路由
app.MapControllers();

// 配置SignalR Hub
app.MapHub<RadarHub>("/radarhub");

// 启动后台服务进行数据清理
var serviceProvider = app.Services;
Task.Run(async () =>
{
    while (true)
    {
        try
        {
            using var scope = serviceProvider.CreateScope();
            var radarService = scope.ServiceProvider.GetRequiredService<IRadarService>();
            await radarService.CleanupExpiredDataAsync();
            await Task.Delay(TimeSpan.FromMinutes(1)); // 每分钟清理一次
        }
        catch (Exception ex)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
            logger.LogError(ex, "后台数据清理任务异常");
            await Task.Delay(TimeSpan.FromMinutes(5)); // 异常时等待5分钟再重试
        }
    }
});

app.Run();
