namespace DeltaForce_Radar.Models
{
    /// <summary>
    /// 地图信息模型
    /// </summary>
    public class MapInfo
    {
        public string Name { get; set; } = string.Empty;
        public float Width { get; set; } = 1000f;
        public float Height { get; set; } = 1000f;
        public string BackgroundImage { get; set; } = string.Empty;
        public float Scale { get; set; } = 1.0f; // 地图缩放比例
    }

    /// <summary>
    /// 雷达数据模型 - 包含所有雷达显示的数据
    /// </summary>
    public class RadarData
    {
        public List<PlayerInfo> Players { get; set; } = new List<PlayerInfo>();
        public List<ItemInfo> Items { get; set; } = new List<ItemInfo>();
        public MapInfo MapInfo { get; set; } = new MapInfo();
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// 获取自己的玩家信息
        /// </summary>
        public PlayerInfo? GetSelfPlayer()
        {
            return Players.FirstOrDefault(p => p.Type == PlayerType.Self);
        }

        /// <summary>
        /// 获取队友列表
        /// </summary>
        public List<PlayerInfo> GetTeammates()
        {
            return Players.Where(p => p.Type == PlayerType.Teammate).ToList();
        }

        /// <summary>
        /// 获取敌人列表
        /// </summary>
        public List<PlayerInfo> GetEnemies()
        {
            return Players.Where(p => p.Type == PlayerType.Enemy).ToList();
        }

        /// <summary>
        /// 获取指定类型的物品列表
        /// </summary>
        public List<ItemInfo> GetItemsByType(ItemType type)
        {
            return Items.Where(i => i.Type == type && i.IsAvailable && !i.IsExpired()).ToList();
        }

        /// <summary>
        /// 获取撤离点列表
        /// </summary>
        public List<ItemInfo> GetExtractionPoints()
        {
            return GetItemsByType(ItemType.ExtractionPoint);
        }

        /// <summary>
        /// 清理过期数据
        /// </summary>
        public void CleanupExpiredData()
        {
            // 移除过期的物品
            Items.RemoveAll(i => i.IsExpired());
            
            // 移除过期的玩家数据
            Players.RemoveAll(p => p.IsDataStale());
        }

        /// <summary>
        /// 获取数据统计信息
        /// </summary>
        public object GetStatistics()
        {
            return new
            {
                TotalPlayers = Players.Count,
                Teammates = GetTeammates().Count,
                Enemies = GetEnemies().Count,
                TotalItems = Items.Count(i => i.IsAvailable && !i.IsExpired()),
                ExtractionPoints = GetExtractionPoints().Count,
                LastUpdate = Timestamp
            };
        }
    }
}
