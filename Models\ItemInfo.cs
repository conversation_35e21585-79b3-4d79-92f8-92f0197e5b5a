namespace DeltaForce_Radar.Models
{
    /// <summary>
    /// 物品类型枚举
    /// </summary>
    public enum ItemType
    {
        Weapon = 0,         // 武器
        Ammo = 1,           // 弹药
        Medical = 2,        // 医疗用品
        Equipment = 3,      // 装备
        Vehicle = 4,        // 载具
        ExtractionPoint = 5, // 撤离点
        HighValue = 6       // 高价值物品
    }

    /// <summary>
    /// 物品信息模型
    /// </summary>
    public class ItemInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public Position Position { get; set; } = new Position();
        public ItemType Type { get; set; }
        public int Value { get; set; } = 0; // 物品价值或重要性
        public bool IsAvailable { get; set; } = true; // 是否可用
        public string Description { get; set; } = string.Empty;
        public DateTime SpawnTime { get; set; } = DateTime.Now;
        public DateTime? ExpiryTime { get; set; } // 过期时间（可选）

        /// <summary>
        /// 获取物品类型的显示名称
        /// </summary>
        public string GetTypeDisplayName()
        {
            return Type switch
            {
                ItemType.Weapon => "武器",
                ItemType.Ammo => "弹药",
                ItemType.Medical => "医疗",
                ItemType.Equipment => "装备",
                ItemType.Vehicle => "载具",
                ItemType.ExtractionPoint => "撤离点",
                ItemType.HighValue => "高价值",
                _ => "未知"
            };
        }

        /// <summary>
        /// 获取物品的图标名称（用于前端显示）
        /// </summary>
        public string GetIconName()
        {
            return Type switch
            {
                ItemType.Weapon => "weapon",
                ItemType.Ammo => "ammo",
                ItemType.Medical => "medical",
                ItemType.Equipment => "equipment",
                ItemType.Vehicle => "vehicle",
                ItemType.ExtractionPoint => "extraction",
                ItemType.HighValue => "treasure",
                _ => "unknown"
            };
        }

        /// <summary>
        /// 检查物品是否已过期
        /// </summary>
        public bool IsExpired()
        {
            return ExpiryTime.HasValue && DateTime.Now > ExpiryTime.Value;
        }

        /// <summary>
        /// 获取物品的优先级（用于显示排序）
        /// </summary>
        public int GetPriority()
        {
            return Type switch
            {
                ItemType.ExtractionPoint => 10,
                ItemType.HighValue => 9,
                ItemType.Vehicle => 8,
                ItemType.Weapon => 7,
                ItemType.Medical => 6,
                ItemType.Equipment => 5,
                ItemType.Ammo => 4,
                _ => 1
            };
        }
    }
}
