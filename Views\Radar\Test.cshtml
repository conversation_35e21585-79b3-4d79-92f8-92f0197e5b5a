@{
    ViewData["Title"] = "测试数据";
}

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-info">🧪 测试数据生成器</h2>
                <a href="@Url.Action("Index", "Radar")" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> 返回雷达
                </a>
            </div>
        </div>
    </div>

    @if (ViewBag.Error != null)
    {
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle"></i> @ViewBag.Error
        </div>
    }

    @if (ViewBag.Message != null)
    {
        <div class="alert alert-success" role="alert">
            <i class="fas fa-check-circle"></i> @ViewBag.Message
        </div>
    }

    <div class="row g-4">
        <!-- 快速测试 -->
        <div class="col-lg-6">
            <div class="settings-section">
                <h5><i class="fas fa-bolt text-warning"></i> 快速测试</h5>
                <p class="text-muted">快速生成预设的测试数据场景</p>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-success" onclick="generateTestData('basic')">
                        <i class="fas fa-play"></i> 基础场景 (5玩家 + 10物品)
                    </button>
                    <button class="btn btn-warning" onclick="generateTestData('combat')">
                        <i class="fas fa-fire"></i> 战斗场景 (10玩家 + 20物品)
                    </button>
                    <button class="btn btn-danger" onclick="generateTestData('intense')">
                        <i class="fas fa-bomb"></i> 激烈场景 (20玩家 + 50物品)
                    </button>
                    <button class="btn btn-info" onclick="generateTestData('extraction')">
                        <i class="fas fa-helicopter"></i> 撤离场景 (载具 + 撤离点)
                    </button>
                </div>
            </div>
        </div>

        <!-- 自定义测试 -->
        <div class="col-lg-6">
            <div class="settings-section">
                <h5><i class="fas fa-cogs text-primary"></i> 自定义测试</h5>
                <p class="text-muted">自定义生成测试数据的参数</p>
                
                <form id="custom-test-form">
                    <div class="mb-3">
                        <label for="playerCount" class="form-label">玩家数量</label>
                        <input type="range" class="form-range" id="playerCount" min="1" max="50" value="10">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">1</small>
                            <span id="playerCountValue" class="text-success">10</span>
                            <small class="text-muted">50</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="itemCount" class="form-label">物品数量</label>
                        <input type="range" class="form-range" id="itemCount" min="0" max="100" value="20">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">0</small>
                            <span id="itemCountValue" class="text-success">20</span>
                            <small class="text-muted">100</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="mapSize" class="form-label">地图范围 (米)</label>
                        <input type="range" class="form-range" id="mapSize" min="100" max="2000" step="100" value="1000">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">100m</small>
                            <span id="mapSizeValue" class="text-success">1000m</span>
                            <small class="text-muted">2000m</small>
                        </div>
                    </div>

                    <div class="row g-2 mb-3">
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeVehicles" checked>
                                <label class="form-check-label" for="includeVehicles">包含载具</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeExtraction" checked>
                                <label class="form-check-label" for="includeExtraction">包含撤离点</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeHighValue" checked>
                                <label class="form-check-label" for="includeHighValue">包含高价值物品</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="randomMovement">
                                <label class="form-check-label" for="randomMovement">随机移动</label>
                            </div>
                        </div>
                    </div>

                    <button type="button" class="btn btn-primary w-100" onclick="generateCustomTestData()">
                        <i class="fas fa-magic"></i> 生成自定义测试数据
                    </button>
                </form>
            </div>
        </div>

        <!-- 数据控制 -->
        <div class="col-lg-6">
            <div class="settings-section">
                <h5><i class="fas fa-database text-secondary"></i> 数据控制</h5>
                <p class="text-muted">管理当前的测试数据</p>
                
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-warning" onclick="clearAllData()">
                        <i class="fas fa-trash"></i> 清空所有数据
                    </button>
                    <button class="btn btn-outline-info" onclick="exportTestData()">
                        <i class="fas fa-download"></i> 导出测试数据
                    </button>
                    <button class="btn btn-outline-success" onclick="importTestData()">
                        <i class="fas fa-upload"></i> 导入测试数据
                    </button>
                    <button class="btn btn-outline-primary" onclick="startAutoUpdate()">
                        <i class="fas fa-sync"></i> <span id="auto-update-text">开始自动更新</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 实时统计 -->
        <div class="col-lg-6">
            <div class="settings-section">
                <h5><i class="fas fa-chart-bar text-success"></i> 实时统计</h5>
                <p class="text-muted">当前雷达数据统计信息</p>
                
                <div class="row g-3">
                    <div class="col-6">
                        <div class="bg-dark p-3 rounded text-center">
                            <div class="h4 text-primary mb-1" id="stat-total-players">0</div>
                            <small class="text-muted">总玩家</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-dark p-3 rounded text-center">
                            <div class="h4 text-success mb-1" id="stat-teammates">0</div>
                            <small class="text-muted">队友</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-dark p-3 rounded text-center">
                            <div class="h4 text-danger mb-1" id="stat-enemies">0</div>
                            <small class="text-muted">敌人</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="bg-dark p-3 rounded text-center">
                            <div class="h4 text-warning mb-1" id="stat-total-items">0</div>
                            <small class="text-muted">物品</small>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <small class="text-muted">最后更新: </small>
                    <span id="stat-last-update" class="text-info">--:--:--</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试场景说明 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="settings-section">
                <h5><i class="fas fa-info-circle text-info"></i> 测试场景说明</h5>
                <div class="row g-3">
                    <div class="col-md-6">
                        <h6 class="text-success">基础场景</h6>
                        <ul class="list-unstyled small text-muted">
                            <li>• 5个玩家（1个自己，2个队友，2个敌人）</li>
                            <li>• 10个随机物品</li>
                            <li>• 500米范围内分布</li>
                            <li>• 适合基础功能测试</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-warning">战斗场景</h6>
                        <ul class="list-unstyled small text-muted">
                            <li>• 10个玩家（1个自己，4个队友，5个敌人）</li>
                            <li>• 20个物品（包含武器和医疗用品）</li>
                            <li>• 800米范围内分布</li>
                            <li>• 适合战斗功能测试</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-danger">激烈场景</h6>
                        <ul class="list-unstyled small text-muted">
                            <li>• 20个玩家（1个自己，9个队友，10个敌人）</li>
                            <li>• 50个物品（各种类型）</li>
                            <li>• 1200米范围内分布</li>
                            <li>• 适合性能压力测试</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">撤离场景</h6>
                        <ul class="list-unstyled small text-muted">
                            <li>• 8个玩家（混合类型）</li>
                            <li>• 3个载具，2个撤离点</li>
                            <li>• 高价值物品分布</li>
                            <li>• 适合撤离功能测试</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 文件导入隐藏输入 -->
<input type="file" id="import-file-input" accept=".json" style="display: none;">

@section Scripts {
    <script>
        let autoUpdateInterval = null;
        let isAutoUpdating = false;

        // 实时更新滑块值显示
        document.getElementById('playerCount').addEventListener('input', function() {
            document.getElementById('playerCountValue').textContent = this.value;
        });

        document.getElementById('itemCount').addEventListener('input', function() {
            document.getElementById('itemCountValue').textContent = this.value;
        });

        document.getElementById('mapSize').addEventListener('input', function() {
            document.getElementById('mapSizeValue').textContent = this.value + 'm';
        });

        // 生成预设测试数据
        async function generateTestData(scenario) {
            try {
                const response = await fetch(`/Radar/GenerateTestData?scenario=${scenario}`, {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.text();
                    showMessage('测试数据生成成功！', 'success');
                    updateStatistics();
                } else {
                    showMessage('测试数据生成失败', 'error');
                }
            } catch (error) {
                console.error('生成测试数据失败:', error);
                showMessage('生成测试数据失败: ' + error.message, 'error');
            }
        }

        // 生成自定义测试数据
        async function generateCustomTestData() {
            const playerCount = document.getElementById('playerCount').value;
            const itemCount = document.getElementById('itemCount').value;
            const mapSize = document.getElementById('mapSize').value;
            const includeVehicles = document.getElementById('includeVehicles').checked;
            const includeExtraction = document.getElementById('includeExtraction').checked;
            const includeHighValue = document.getElementById('includeHighValue').checked;
            const randomMovement = document.getElementById('randomMovement').checked;

            const params = new URLSearchParams({
                playerCount,
                itemCount,
                mapSize,
                includeVehicles,
                includeExtraction,
                includeHighValue,
                randomMovement
            });

            try {
                const response = await fetch(`/Radar/GenerateCustomTestData?${params}`, {
                    method: 'POST'
                });

                if (response.ok) {
                    showMessage('自定义测试数据生成成功！', 'success');
                    updateStatistics();
                } else {
                    showMessage('自定义测试数据生成失败', 'error');
                }
            } catch (error) {
                console.error('生成自定义测试数据失败:', error);
                showMessage('生成自定义测试数据失败: ' + error.message, 'error');
            }
        }

        // 清空所有数据
        async function clearAllData() {
            if (confirm('确定要清空所有测试数据吗？')) {
                try {
                    const response = await fetch('/api/radardata/reset', {
                        method: 'POST'
                    });

                    if (response.ok) {
                        showMessage('所有数据已清空', 'success');
                        updateStatistics();
                    } else {
                        showMessage('清空数据失败', 'error');
                    }
                } catch (error) {
                    console.error('清空数据失败:', error);
                    showMessage('清空数据失败: ' + error.message, 'error');
                }
            }
        }

        // 导出测试数据
        async function exportTestData() {
            try {
                const response = await fetch('/api/radardata');
                if (response.ok) {
                    const data = await response.json();
                    const dataStr = JSON.stringify(data, null, 2);
                    const dataBlob = new Blob([dataStr], { type: 'application/json' });
                    
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(dataBlob);
                    link.download = `radar-test-data-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
                    link.click();
                    
                    showMessage('测试数据导出成功', 'success');
                } else {
                    showMessage('导出数据失败', 'error');
                }
            } catch (error) {
                console.error('导出数据失败:', error);
                showMessage('导出数据失败: ' + error.message, 'error');
            }
        }

        // 导入测试数据
        function importTestData() {
            document.getElementById('import-file-input').click();
        }

        // 文件导入处理
        document.getElementById('import-file-input').addEventListener('change', async function(e) {
            const file = e.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const data = JSON.parse(text);
                
                // 这里应该调用API导入数据
                // 暂时显示成功消息
                showMessage('测试数据导入成功', 'success');
                updateStatistics();
            } catch (error) {
                console.error('导入数据失败:', error);
                showMessage('导入数据失败: ' + error.message, 'error');
            }
        });

        // 开始/停止自动更新
        function startAutoUpdate() {
            const button = document.getElementById('auto-update-text');
            
            if (isAutoUpdating) {
                // 停止自动更新
                clearInterval(autoUpdateInterval);
                isAutoUpdating = false;
                button.textContent = '开始自动更新';
                showMessage('自动更新已停止', 'info');
            } else {
                // 开始自动更新
                autoUpdateInterval = setInterval(() => {
                    generateTestData('basic');
                }, 5000); // 每5秒更新一次
                
                isAutoUpdating = true;
                button.textContent = '停止自动更新';
                showMessage('自动更新已开始（每5秒）', 'info');
            }
        }

        // 更新统计信息
        async function updateStatistics() {
            try {
                const response = await fetch('/api/radardata/statistics');
                if (response.ok) {
                    const stats = await response.json();
                    
                    document.getElementById('stat-total-players').textContent = stats.totalPlayers || 0;
                    document.getElementById('stat-teammates').textContent = stats.teammates || 0;
                    document.getElementById('stat-enemies').textContent = stats.enemies || 0;
                    document.getElementById('stat-total-items').textContent = stats.totalItems || 0;
                    document.getElementById('stat-last-update').textContent = new Date().toLocaleTimeString();
                }
            } catch (error) {
                console.error('更新统计信息失败:', error);
            }
        }

        // 显示消息
        function showMessage(message, type) {
            // 创建临时消息元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            // 插入到页面顶部
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alertDiv, container.firstChild);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // 页面加载时更新统计信息
        document.addEventListener('DOMContentLoaded', function() {
            updateStatistics();
            
            // 每30秒自动更新统计信息
            setInterval(updateStatistics, 30000);
        });

        // 页面卸载时清理定时器
        window.addEventListener('beforeunload', function() {
            if (autoUpdateInterval) {
                clearInterval(autoUpdateInterval);
            }
        });
    </script>
}
