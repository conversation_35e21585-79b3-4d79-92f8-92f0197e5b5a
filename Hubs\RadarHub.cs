using Microsoft.AspNetCore.SignalR;
using DeltaForce_Radar.Models;
using DeltaForce_Radar.Services;

namespace DeltaForce_Radar.Hubs
{
    /// <summary>
    /// 雷达实时通信Hub
    /// </summary>
    public class RadarHub : Hub
    {
        private readonly IRadarService _radarService;
        private readonly ILogger<RadarHub> _logger;

        public RadarHub(IRadarService radarService, ILogger<RadarHub> logger)
        {
            _radarService = radarService;
            _logger = logger;
        }

        /// <summary>
        /// 客户端连接时调用
        /// </summary>
        public override async Task OnConnectedAsync()
        {
            _logger.LogInformation($"客户端已连接: {Context.ConnectionId}");
            
            // 发送当前雷达数据给新连接的客户端
            var radarData = await _radarService.GetRadarDataAsync();
            await Clients.Caller.SendAsync("UpdateRadarData", radarData);
            
            await base.OnConnectedAsync();
        }

        /// <summary>
        /// 客户端断开连接时调用
        /// </summary>
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            _logger.LogInformation($"客户端已断开连接: {Context.ConnectionId}");
            
            if (exception != null)
            {
                _logger.LogError(exception, "客户端断开连接时发生异常");
            }
            
            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// 客户端请求雷达数据
        /// </summary>
        public async Task RequestRadarData()
        {
            try
            {
                var radarData = await _radarService.GetRadarDataAsync();
                await Clients.Caller.SendAsync("UpdateRadarData", radarData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取雷达数据时发生异常");
                await Clients.Caller.SendAsync("Error", "获取雷达数据失败");
            }
        }

        /// <summary>
        /// 更新玩家位置
        /// </summary>
        public async Task UpdatePlayerPosition(PlayerInfo player)
        {
            try
            {
                await _radarService.UpdatePlayerAsync(player);
                
                // 广播给所有客户端
                await Clients.All.SendAsync("PlayerUpdated", player);
                
                _logger.LogDebug($"玩家位置已更新: {player.Name} at {player.Position}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新玩家位置时发生异常");
                await Clients.Caller.SendAsync("Error", "更新玩家位置失败");
            }
        }

        /// <summary>
        /// 批量更新玩家数据
        /// </summary>
        public async Task UpdatePlayers(List<PlayerInfo> players)
        {
            try
            {
                await _radarService.UpdatePlayersAsync(players);
                
                // 广播给所有客户端
                await Clients.All.SendAsync("PlayersUpdated", players);
                
                _logger.LogDebug($"批量更新了 {players.Count} 个玩家");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新玩家数据时发生异常");
                await Clients.Caller.SendAsync("Error", "批量更新玩家数据失败");
            }
        }

        /// <summary>
        /// 更新物品信息
        /// </summary>
        public async Task UpdateItem(ItemInfo item)
        {
            try
            {
                await _radarService.UpdateItemAsync(item);
                
                // 广播给所有客户端
                await Clients.All.SendAsync("ItemUpdated", item);
                
                _logger.LogDebug($"物品已更新: {item.Name} at {item.Position}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新物品信息时发生异常");
                await Clients.Caller.SendAsync("Error", "更新物品信息失败");
            }
        }

        /// <summary>
        /// 批量更新物品数据
        /// </summary>
        public async Task UpdateItems(List<ItemInfo> items)
        {
            try
            {
                await _radarService.UpdateItemsAsync(items);
                
                // 广播给所有客户端
                await Clients.All.SendAsync("ItemsUpdated", items);
                
                _logger.LogDebug($"批量更新了 {items.Count} 个物品");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量更新物品数据时发生异常");
                await Clients.Caller.SendAsync("Error", "批量更新物品数据失败");
            }
        }

        /// <summary>
        /// 移除玩家
        /// </summary>
        public async Task RemovePlayer(string playerId)
        {
            try
            {
                await _radarService.RemovePlayerAsync(playerId);
                
                // 广播给所有客户端
                await Clients.All.SendAsync("PlayerRemoved", playerId);
                
                _logger.LogDebug($"玩家已移除: {playerId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除玩家时发生异常");
                await Clients.Caller.SendAsync("Error", "移除玩家失败");
            }
        }

        /// <summary>
        /// 移除物品
        /// </summary>
        public async Task RemoveItem(string itemId)
        {
            try
            {
                await _radarService.RemoveItemAsync(itemId);
                
                // 广播给所有客户端
                await Clients.All.SendAsync("ItemRemoved", itemId);
                
                _logger.LogDebug($"物品已移除: {itemId}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除物品时发生异常");
                await Clients.Caller.SendAsync("Error", "移除物品失败");
            }
        }

        /// <summary>
        /// 获取统计信息
        /// </summary>
        public async Task GetStatistics()
        {
            try
            {
                var stats = await _radarService.GetStatisticsAsync();
                await Clients.Caller.SendAsync("StatisticsUpdated", stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取统计信息时发生异常");
                await Clients.Caller.SendAsync("Error", "获取统计信息失败");
            }
        }

        /// <summary>
        /// 加入特定组（用于分组广播）
        /// </summary>
        public async Task JoinGroup(string groupName)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            _logger.LogDebug($"客户端 {Context.ConnectionId} 加入组 {groupName}");
        }

        /// <summary>
        /// 离开特定组
        /// </summary>
        public async Task LeaveGroup(string groupName)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            _logger.LogDebug($"客户端 {Context.ConnectionId} 离开组 {groupName}");
        }
    }
}
