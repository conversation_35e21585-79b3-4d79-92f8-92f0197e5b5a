namespace DeltaForce_Radar.Models
{
    /// <summary>
    /// 玩家类型枚举
    /// </summary>
    public enum PlayerType
    {
        Self = 0,       // 自己
        Teammate = 1,   // 队友
        Enemy = 2       // 敌人
    }

    /// <summary>
    /// 玩家信息模型
    /// </summary>
    public class PlayerInfo
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public Position Position { get; set; } = new Position();
        public float Rotation { get; set; } // 朝向角度（度）
        public PlayerType Type { get; set; }
        public int Health { get; set; } = 100;
        public int Armor { get; set; } = 0;
        public bool IsAlive { get; set; } = true;
        public string WeaponName { get; set; } = string.Empty;
        public DateTime LastUpdateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 获取玩家状态描述
        /// </summary>
        public string GetStatusDescription()
        {
            if (!IsAlive) return "已阵亡";
            if (Health <= 25) return "重伤";
            if (Health <= 50) return "中伤";
            if (Health <= 75) return "轻伤";
            return "健康";
        }

        /// <summary>
        /// 获取玩家类型的显示名称
        /// </summary>
        public string GetTypeDisplayName()
        {
            return Type switch
            {
                PlayerType.Self => "自己",
                PlayerType.Teammate => "队友",
                PlayerType.Enemy => "敌人",
                _ => "未知"
            };
        }

        /// <summary>
        /// 检查玩家数据是否过期（超过5秒未更新）
        /// </summary>
        public bool IsDataStale()
        {
            return DateTime.Now - LastUpdateTime > TimeSpan.FromSeconds(5);
        }
    }
}
