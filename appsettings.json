{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.AspNetCore.SignalR": "Debug"}}, "AllowedHosts": "*", "RadarSettings": {"DefaultConfigPath": "Config/radar-config.json", "DataRetentionMinutes": 5, "MaxPlayersPerSession": 100, "MaxItemsPerSession": 500, "EnableTestDataGeneration": true, "AutoCleanupInterval": 60000}, "SignalR": {"EnableDetailedErrors": true, "MaximumReceiveMessageSize": 32768, "StreamBufferCapacity": 10}}