# 三角洲行动网页雷达技术方案文档

## 项目概述

本项目旨在开发一个基于Web的三角洲行动游戏雷达系统，提供实时地图显示、玩家位置追踪、物品标记等功能。

## 技术架构

### 整体架构
- **架构模式**: MVC + Web API 混合架构
- **通信方式**: SignalR 实时双向通信
- **部署方式**: 单体应用，支持跨平台部署

### 技术栈选择

#### 后端技术栈
- **框架**: .NET 9.0
- **Web框架**: ASP.NET Core MVC + Web API
- **实时通信**: SignalR Hub
- **数据存储**: 
  - IMemoryCache (运行时数据)
  - JSON文件 (配置持久化)
- **依赖注入**: 内置DI容器

#### 前端技术栈
- **页面模板**: Razor Views
- **地图绘制**: Konva.js (Canvas 2D库)
- **实时通信**: SignalR JavaScript客户端
- **UI框架**: Bootstrap 5 (可选)
- **脚本语言**: JavaScript/TypeScript

## 项目结构

```
DeltaForce_Radar/
├── Controllers/
│   ├── HomeController.cs          # 主页控制器
│   ├── RadarController.cs         # 雷达页面控制器
│   └── Api/
│       ├── RadarDataController.cs # 雷达数据API
│       └── ConfigController.cs    # 配置管理API
├── Hubs/
│   └── RadarHub.cs               # SignalR实时通信Hub
├── Models/
│   ├── RadarData.cs              # 雷达数据模型
│   ├── PlayerInfo.cs             # 玩家信息模型
│   ├── ItemInfo.cs               # 物品信息模型
│   └── ConfigModel.cs            # 配置模型
├── Services/
│   ├── IRadarService.cs          # 雷达服务接口
│   ├── RadarService.cs           # 雷达服务实现
│   ├── IConfigService.cs         # 配置服务接口
│   └── ConfigService.cs          # 配置服务实现
├── Views/
│   ├── Home/
│   │   └── Index.cshtml          # 主页视图
│   ├── Radar/
│   │   └── Index.cshtml          # 雷达页面视图
│   └── Shared/
│       ├── _Layout.cshtml        # 布局模板
│       └── _ViewImports.cshtml   # 视图导入
├── wwwroot/
│   ├── js/
│   │   ├── konva.min.js          # Konva.js库
│   │   ├── signalr.min.js        # SignalR客户端
│   │   └── radar.js              # 雷达逻辑
│   ├── css/
│   │   └── radar.css             # 雷达样式
│   └── images/
│       └── icons/                # 图标资源
├── Config/
│   └── radar-config.json         # 雷达配置文件
├── Program.cs                    # 应用程序入口
└── appsettings.json             # 应用配置
```

## 核心功能模块

### 1. 地图渲染模块
- **技术**: Konva.js Canvas 2D
- **功能**: 
  - 地图背景绘制
  - 玩家位置标记
  - 物品图标显示
  - 实时位置更新
- **性能**: 支持60fps流畅渲染

### 2. 实时通信模块
- **技术**: SignalR Hub
- **功能**:
  - 实时数据推送
  - 客户端状态同步
  - 事件广播
- **连接**: WebSocket优先，降级到长轮询

### 3. 数据管理模块
- **内存缓存**: 高频数据(玩家位置、物品状态)
- **配置文件**: 低频数据(地图配置、UI设置)
- **数据流**: API -> Service -> Hub -> Client

### 4. API接口模块
- **RESTful API**: 标准HTTP接口
- **数据格式**: JSON
- **认证**: 可扩展Token认证

## 数据模型设计

### 雷达数据模型
```csharp
public class RadarData
{
    public List<PlayerInfo> Players { get; set; }
    public List<ItemInfo> Items { get; set; }
    public MapInfo MapInfo { get; set; }
    public DateTime Timestamp { get; set; }
}

public class PlayerInfo
{
    public string Id { get; set; }
    public string Name { get; set; }
    public Position Position { get; set; }
    public float Rotation { get; set; }
    public PlayerType Type { get; set; } // Self, Teammate, Enemy
    public int Health { get; set; }
}

public class ItemInfo
{
    public string Id { get; set; }
    public string Name { get; set; }
    public Position Position { get; set; }
    public ItemType Type { get; set; }
    public int Value { get; set; }
}
```

## 前端架构设计

### Konva.js 图层结构
```javascript
// 图层层次结构
stage
├── backgroundLayer    // 地图背景层
├── itemLayer         // 物品标记层
├── playerLayer       // 玩家标记层
└── uiLayer          // UI控件层
```

### SignalR 客户端事件
```javascript
// 接收事件
connection.on("UpdateRadarData", updateRadarDisplay);
connection.on("PlayerJoined", addPlayer);
connection.on("PlayerLeft", removePlayer);
connection.on("ItemSpawned", addItem);

// 发送事件
connection.invoke("RequestRadarData");
connection.invoke("UpdatePlayerPosition", position);
```

## 部署配置

### 开发环境
- .NET 9 SDK
- Visual Studio 2024 / VS Code
- 现代浏览器 (Chrome, Firefox, Edge)

### 生产环境
- Windows Server / Linux
- IIS / Nginx + Kestrel
- HTTPS 支持

## 性能优化策略

### 后端优化
- 内存缓存减少计算开销
- SignalR连接池管理
- 数据压缩传输

### 前端优化
- Konva.js对象池复用
- 图层缓存机制
- 按需更新策略

## 扩展性设计

### 功能扩展
- 插件化配置系统
- 多地图支持
- 自定义标记类型

### 技术扩展
- 数据库集成接口
- 外部数据源适配
- 移动端适配

## 安全考虑

### 数据安全
- 输入验证和过滤
- XSS防护
- CSRF保护

### 通信安全
- HTTPS强制
- SignalR连接认证
- API访问控制

## 开发计划

### 第一阶段 (基础框架)
1. 项目结构搭建
2. 基础MVC页面
3. SignalR通信建立
4. Konva.js地图初始化

### 第二阶段 (核心功能)
1. 雷达数据模型
2. 实时数据更新
3. 基础地图绘制
4. 玩家位置显示

### 第三阶段 (功能完善)
1. 物品标记系统
2. UI交互优化
3. 配置管理
4. 性能优化

### 第四阶段 (测试部署)
1. 功能测试
2. 性能测试
3. 部署配置
4. 文档完善

---

**文档版本**: v1.0  
**创建日期**: 2025-01-05  
**最后更新**: 2025-01-05
