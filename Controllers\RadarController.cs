using Microsoft.AspNetCore.Mvc;
using DeltaForce_Radar.Services;

namespace DeltaForce_Radar.Controllers
{
    /// <summary>
    /// 雷达页面控制器
    /// </summary>
    public class RadarController : Controller
    {
        private readonly IRadarService _radarService;
        private readonly ILogger<RadarController> _logger;

        public RadarController(IRadarService radarService, ILogger<RadarController> logger)
        {
            _radarService = radarService;
            _logger = logger;
        }

        /// <summary>
        /// 雷达主页面
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                var radarData = await _radarService.GetRadarDataAsync();
                return View(radarData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载雷达页面时发生异常");
                ViewBag.Error = "加载雷达数据失败";
                return View();
            }
        }

        /// <summary>
        /// 全屏雷达页面
        /// </summary>
        public async Task<IActionResult> Fullscreen()
        {
            try
            {
                var radarData = await _radarService.GetRadarDataAsync();
                return View(radarData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载全屏雷达页面时发生异常");
                ViewBag.Error = "加载雷达数据失败";
                return View();
            }
        }

        /// <summary>
        /// 雷达设置页面
        /// </summary>
        public IActionResult Settings()
        {
            return View();
        }

        /// <summary>
        /// 测试页面（用于开发调试）
        /// </summary>
        public async Task<IActionResult> Test()
        {
            try
            {
                // 生成测试数据
                await GenerateTestDataAsync();
                
                var radarData = await _radarService.GetRadarDataAsync();
                ViewBag.Message = "测试数据已生成";
                return View("Index", radarData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成测试数据时发生异常");
                ViewBag.Error = "生成测试数据失败";
                return View("Index");
            }
        }

        /// <summary>
        /// 生成测试数据
        /// </summary>
        private async Task GenerateTestDataAsync()
        {
            var random = new Random();
            
            // 生成测试玩家
            var players = new List<DeltaForce_Radar.Models.PlayerInfo>
            {
                new() {
                    Id = "player_self",
                    Name = "我自己",
                    Position = new(500, 500, 0),
                    Rotation = 0,
                    Type = DeltaForce_Radar.Models.PlayerType.Self,
                    Health = 100,
                    Armor = 50,
                    IsAlive = true,
                    WeaponName = "AK-47"
                },
                new() {
                    Id = "player_teammate1",
                    Name = "队友1",
                    Position = new(random.Next(400, 600), random.Next(400, 600), 0),
                    Rotation = random.Next(0, 360),
                    Type = DeltaForce_Radar.Models.PlayerType.Teammate,
                    Health = random.Next(50, 100),
                    Armor = random.Next(0, 100),
                    IsAlive = true,
                    WeaponName = "M4A1"
                },
                new() {
                    Id = "player_enemy1",
                    Name = "敌人1",
                    Position = new(random.Next(300, 700), random.Next(300, 700), 0),
                    Rotation = random.Next(0, 360),
                    Type = DeltaForce_Radar.Models.PlayerType.Enemy,
                    Health = random.Next(30, 100),
                    Armor = random.Next(0, 50),
                    IsAlive = true,
                    WeaponName = "AWP"
                }
            };

            // 生成测试物品
            var items = new List<DeltaForce_Radar.Models.ItemInfo>
            {
                new() {
                    Id = "item_weapon1",
                    Name = "AK-47",
                    Position = new(random.Next(200, 800), random.Next(200, 800), 0),
                    Type = DeltaForce_Radar.Models.ItemType.Weapon,
                    Value = 100,
                    IsAvailable = true,
                    Description = "突击步枪"
                },
                new() {
                    Id = "item_medical1",
                    Name = "医疗包",
                    Position = new(random.Next(200, 800), random.Next(200, 800), 0),
                    Type = DeltaForce_Radar.Models.ItemType.Medical,
                    Value = 50,
                    IsAvailable = true,
                    Description = "恢复生命值"
                },
                new() {
                    Id = "item_extraction1",
                    Name = "撤离点A",
                    Position = new(100, 100, 0),
                    Type = DeltaForce_Radar.Models.ItemType.ExtractionPoint,
                    Value = 1000,
                    IsAvailable = true,
                    Description = "主要撤离点"
                },
                new() {
                    Id = "item_vehicle1",
                    Name = "装甲车",
                    Position = new(random.Next(300, 700), random.Next(300, 700), 0),
                    Type = DeltaForce_Radar.Models.ItemType.Vehicle,
                    Value = 200,
                    IsAvailable = true,
                    Description = "重型载具"
                }
            };

            // 更新数据
            await _radarService.UpdatePlayersAsync(players);
            await _radarService.UpdateItemsAsync(items);
            
            _logger.LogInformation($"已生成测试数据: {players.Count} 个玩家, {items.Count} 个物品");
        }

        /// <summary>
        /// 生成预设测试数据
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> GenerateTestData(string scenario = "basic")
        {
            try
            {
                var random = new Random();
                var players = new List<DeltaForce_Radar.Models.PlayerInfo>();
                var items = new List<DeltaForce_Radar.Models.ItemInfo>();

                switch (scenario.ToLower())
                {
                    case "basic":
                        // 基础场景: 5玩家 + 10物品
                        players = GenerateTestPlayers(5, random);
                        items = GenerateTestItems(10, random);
                        break;

                    case "combat":
                        // 战斗场景: 10玩家 + 20物品
                        players = GenerateTestPlayers(10, random);
                        items = GenerateTestItems(20, random);
                        break;

                    case "intense":
                        // 激烈场景: 20玩家 + 50物品
                        players = GenerateTestPlayers(20, random);
                        items = GenerateTestItems(50, random);
                        break;

                    case "extraction":
                        // 撤离场景: 载具 + 撤离点
                        players = GenerateTestPlayers(8, random);
                        items = GenerateTestItems(15, random);
                        // 添加载具和撤离点
                        items.AddRange(GenerateVehicles(3, random));
                        items.AddRange(GenerateExtractionPoints(2, random));
                        break;

                    default:
                        players = GenerateTestPlayers(5, random);
                        items = GenerateTestItems(10, random);
                        break;
                }

                // 更新数据
                await _radarService.UpdatePlayersAsync(players);
                await _radarService.UpdateItemsAsync(items);

                _logger.LogInformation($"已生成 {scenario} 测试数据: {players.Count} 个玩家, {items.Count} 个物品");
                return Ok(new { message = $"已生成 {scenario} 测试数据", playerCount = players.Count, itemCount = items.Count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成测试数据失败");
                return StatusCode(500, new { error = "生成测试数据失败: " + ex.Message });
            }
        }

        /// <summary>
        /// 生成自定义测试数据
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> GenerateCustomTestData(
            int playerCount = 5,
            int itemCount = 10,
            float mapSize = 1000,
            bool includeVehicles = false,
            bool includeExtraction = false,
            bool includeHighValue = false,
            bool randomMovement = false)
        {
            try
            {
                var random = new Random();
                var players = GenerateTestPlayers(playerCount, random, mapSize);
                var items = GenerateTestItems(itemCount, random, mapSize);

                if (includeVehicles)
                {
                    items.AddRange(GenerateVehicles(3, random, mapSize));
                }

                if (includeExtraction)
                {
                    items.AddRange(GenerateExtractionPoints(2, random, mapSize));
                }

                if (includeHighValue)
                {
                    items.AddRange(GenerateHighValueItems(5, random, mapSize));
                }

                // 更新数据
                await _radarService.UpdatePlayersAsync(players);
                await _radarService.UpdateItemsAsync(items);

                _logger.LogInformation($"已生成自定义测试数据: {players.Count} 个玩家, {items.Count} 个物品");
                return Ok(new { message = "自定义测试数据生成成功", playerCount = players.Count, itemCount = items.Count });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成自定义测试数据失败");
                return StatusCode(500, new { error = "生成自定义测试数据失败: " + ex.Message });
            }
        }

        private List<DeltaForce_Radar.Models.PlayerInfo> GenerateTestPlayers(int count, Random random, float mapSize = 1000f)
        {
            var players = new List<DeltaForce_Radar.Models.PlayerInfo>();
            var playerNames = new[] { "Alpha", "Bravo", "Charlie", "Delta", "Echo", "Foxtrot", "Golf", "Hotel", "India", "Juliet", "Kilo", "Lima", "Mike", "November", "Oscar", "Papa", "Quebec", "Romeo", "Sierra", "Tango" };
            var weapons = new[] { "AK-74", "M4A1", "SCAR-H", "MP5", "P90", "AWM", "M249", "RPG-7", "手枪", "霰弹枪" };

            for (int i = 0; i < count; i++)
            {
                var playerType = random.NextDouble() < 0.3 ? DeltaForce_Radar.Models.PlayerType.Teammate : DeltaForce_Radar.Models.PlayerType.Enemy;
                if (i == 0) playerType = DeltaForce_Radar.Models.PlayerType.Self; // 第一个玩家是自己

                players.Add(new DeltaForce_Radar.Models.PlayerInfo
                {
                    Id = $"player_{i + 1}",
                    Name = playerNames[i % playerNames.Length] + $"_{i + 1}",
                    Position = new DeltaForce_Radar.Models.Position
                    {
                        X = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Y = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Z = (float)(random.NextDouble() * 50)
                    },
                    Rotation = (float)(random.NextDouble() * 360),
                    Type = playerType,
                    Health = random.Next(20, 101),
                    Armor = random.Next(0, 101),
                    IsAlive = random.NextDouble() < 0.9, // 90% 概率存活
                    WeaponName = weapons[random.Next(weapons.Length)],
                    LastUpdateTime = DateTime.Now.AddSeconds(-random.Next(0, 30))
                });
            }

            return players;
        }

        private List<DeltaForce_Radar.Models.ItemInfo> GenerateTestItems(int count, Random random, float mapSize = 1000f)
        {
            var items = new List<DeltaForce_Radar.Models.ItemInfo>();
            var itemNames = new[] { "医疗包", "弹药箱", "护甲", "头盔", "背包", "武器配件", "手雷", "烟雾弹", "夜视仪", "通讯设备" };

            for (int i = 0; i < count; i++)
            {
                items.Add(new DeltaForce_Radar.Models.ItemInfo
                {
                    Id = $"item_{i + 1}",
                    Name = itemNames[random.Next(itemNames.Length)],
                    Position = new DeltaForce_Radar.Models.Position
                    {
                        X = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Y = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Z = (float)(random.NextDouble() * 10)
                    },
                    Type = (DeltaForce_Radar.Models.ItemType)random.Next(0, 4),
                    Value = random.Next(100, 1001),
                    IsAvailable = true,
                    Description = "测试物品",
                    SpawnTime = DateTime.Now.AddSeconds(-random.Next(0, 60))
                });
            }

            return items;
        }

        private List<DeltaForce_Radar.Models.ItemInfo> GenerateVehicles(int count, Random random, float mapSize = 1000f)
        {
            var vehicles = new List<DeltaForce_Radar.Models.ItemInfo>();
            var vehicleNames = new[] { "装甲车", "直升机", "摩托车", "卡车", "坦克" };

            for (int i = 0; i < count; i++)
            {
                vehicles.Add(new DeltaForce_Radar.Models.ItemInfo
                {
                    Id = $"vehicle_{i + 1}",
                    Name = vehicleNames[random.Next(vehicleNames.Length)],
                    Position = new DeltaForce_Radar.Models.Position
                    {
                        X = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Y = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Z = 0
                    },
                    Type = DeltaForce_Radar.Models.ItemType.Vehicle,
                    Value = random.Next(5000, 10001),
                    IsAvailable = true,
                    Description = "载具",
                    SpawnTime = DateTime.Now
                });
            }

            return vehicles;
        }

        private List<DeltaForce_Radar.Models.ItemInfo> GenerateExtractionPoints(int count, Random random, float mapSize = 1000f)
        {
            var extractions = new List<DeltaForce_Radar.Models.ItemInfo>();

            for (int i = 0; i < count; i++)
            {
                extractions.Add(new DeltaForce_Radar.Models.ItemInfo
                {
                    Id = $"extraction_{i + 1}",
                    Name = $"撤离点 {i + 1}",
                    Position = new DeltaForce_Radar.Models.Position
                    {
                        X = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Y = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Z = 0
                    },
                    Type = DeltaForce_Radar.Models.ItemType.ExtractionPoint,
                    Value = 0,
                    IsAvailable = true,
                    Description = "撤离点",
                    SpawnTime = DateTime.Now
                });
            }

            return extractions;
        }

        private List<DeltaForce_Radar.Models.ItemInfo> GenerateHighValueItems(int count, Random random, float mapSize = 1000f)
        {
            var highValueItems = new List<DeltaForce_Radar.Models.ItemInfo>();
            var itemNames = new[] { "黄金", "钻石", "机密文件", "特殊武器", "稀有装备" };

            for (int i = 0; i < count; i++)
            {
                highValueItems.Add(new DeltaForce_Radar.Models.ItemInfo
                {
                    Id = $"highvalue_{i + 1}",
                    Name = itemNames[random.Next(itemNames.Length)],
                    Position = new DeltaForce_Radar.Models.Position
                    {
                        X = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Y = (float)(random.NextDouble() * mapSize - mapSize / 2),
                        Z = (float)(random.NextDouble() * 5)
                    },
                    Type = DeltaForce_Radar.Models.ItemType.HighValue,
                    Value = random.Next(2000, 5001),
                    IsAvailable = true,
                    Description = "高价值物品",
                    SpawnTime = DateTime.Now
                });
            }

            return highValueItems;
        }
    }
}
