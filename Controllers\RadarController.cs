using Microsoft.AspNetCore.Mvc;
using DeltaForce_Radar.Services;

namespace DeltaForce_Radar.Controllers
{
    /// <summary>
    /// 雷达页面控制器
    /// </summary>
    public class RadarController : Controller
    {
        private readonly IRadarService _radarService;
        private readonly ILogger<RadarController> _logger;

        public RadarController(IRadarService radarService, ILogger<RadarController> logger)
        {
            _radarService = radarService;
            _logger = logger;
        }

        /// <summary>
        /// 雷达主页面
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                var radarData = await _radarService.GetRadarDataAsync();
                return View(radarData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载雷达页面时发生异常");
                ViewBag.Error = "加载雷达数据失败";
                return View();
            }
        }

        /// <summary>
        /// 全屏雷达页面
        /// </summary>
        public async Task<IActionResult> Fullscreen()
        {
            try
            {
                var radarData = await _radarService.GetRadarDataAsync();
                return View(radarData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载全屏雷达页面时发生异常");
                ViewBag.Error = "加载雷达数据失败";
                return View();
            }
        }

        /// <summary>
        /// 雷达设置页面
        /// </summary>
        public IActionResult Settings()
        {
            return View();
        }

        /// <summary>
        /// 测试页面（用于开发调试）
        /// </summary>
        public async Task<IActionResult> Test()
        {
            try
            {
                // 生成测试数据
                await GenerateTestDataAsync();
                
                var radarData = await _radarService.GetRadarDataAsync();
                ViewBag.Message = "测试数据已生成";
                return View("Index", radarData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "生成测试数据时发生异常");
                ViewBag.Error = "生成测试数据失败";
                return View("Index");
            }
        }

        /// <summary>
        /// 生成测试数据
        /// </summary>
        private async Task GenerateTestDataAsync()
        {
            var random = new Random();
            
            // 生成测试玩家
            var players = new List<DeltaForce_Radar.Models.PlayerInfo>
            {
                new() {
                    Id = "player_self",
                    Name = "我自己",
                    Position = new(500, 500, 0),
                    Rotation = 0,
                    Type = DeltaForce_Radar.Models.PlayerType.Self,
                    Health = 100,
                    Armor = 50,
                    IsAlive = true,
                    WeaponName = "AK-47"
                },
                new() {
                    Id = "player_teammate1",
                    Name = "队友1",
                    Position = new(random.Next(400, 600), random.Next(400, 600), 0),
                    Rotation = random.Next(0, 360),
                    Type = DeltaForce_Radar.Models.PlayerType.Teammate,
                    Health = random.Next(50, 100),
                    Armor = random.Next(0, 100),
                    IsAlive = true,
                    WeaponName = "M4A1"
                },
                new() {
                    Id = "player_enemy1",
                    Name = "敌人1",
                    Position = new(random.Next(300, 700), random.Next(300, 700), 0),
                    Rotation = random.Next(0, 360),
                    Type = DeltaForce_Radar.Models.PlayerType.Enemy,
                    Health = random.Next(30, 100),
                    Armor = random.Next(0, 50),
                    IsAlive = true,
                    WeaponName = "AWP"
                }
            };

            // 生成测试物品
            var items = new List<DeltaForce_Radar.Models.ItemInfo>
            {
                new() {
                    Id = "item_weapon1",
                    Name = "AK-47",
                    Position = new(random.Next(200, 800), random.Next(200, 800), 0),
                    Type = DeltaForce_Radar.Models.ItemType.Weapon,
                    Value = 100,
                    IsAvailable = true,
                    Description = "突击步枪"
                },
                new() {
                    Id = "item_medical1",
                    Name = "医疗包",
                    Position = new(random.Next(200, 800), random.Next(200, 800), 0),
                    Type = DeltaForce_Radar.Models.ItemType.Medical,
                    Value = 50,
                    IsAvailable = true,
                    Description = "恢复生命值"
                },
                new() {
                    Id = "item_extraction1",
                    Name = "撤离点A",
                    Position = new(100, 100, 0),
                    Type = DeltaForce_Radar.Models.ItemType.ExtractionPoint,
                    Value = 1000,
                    IsAvailable = true,
                    Description = "主要撤离点"
                },
                new() {
                    Id = "item_vehicle1",
                    Name = "装甲车",
                    Position = new(random.Next(300, 700), random.Next(300, 700), 0),
                    Type = DeltaForce_Radar.Models.ItemType.Vehicle,
                    Value = 200,
                    IsAvailable = true,
                    Description = "重型载具"
                }
            };

            // 更新数据
            await _radarService.UpdatePlayersAsync(players);
            await _radarService.UpdateItemsAsync(items);
            
            _logger.LogInformation($"已生成测试数据: {players.Count} 个玩家, {items.Count} 个物品");
        }
    }
}
