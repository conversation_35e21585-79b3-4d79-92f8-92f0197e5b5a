@model RadarConfig
@{
    ViewData["Title"] = "雷达设置";
}

<div class="container-fluid p-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-success">⚙️ 雷达系统设置</h2>
                <div>
                    <button type="button" class="btn btn-outline-warning me-2" onclick="resetToDefaults()">
                        <i class="fas fa-undo"></i> 重置默认
                    </button>
                    <a href="@Url.Action("Index", "Radar")" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> 返回雷达
                    </a>
                </div>
            </div>
        </div>
    </div>

    @if (ViewBag.Error != null)
    {
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle"></i> @ViewBag.Error
        </div>
    }

    @if (ViewBag.Message != null)
    {
        <div class="alert alert-success" role="alert">
            <i class="fas fa-check-circle"></i> @ViewBag.Message
        </div>
    }

    <form id="settings-form" method="post" asp-action="SaveSettings">
        <div class="row g-4">
            <!-- 显示设置 -->
            <div class="col-lg-6">
                <div class="settings-section">
                    <h5><i class="fas fa-eye text-primary"></i> 显示设置</h5>
                    
                    <div class="mb-3">
                        <label for="radarSize" class="form-label">雷达尺寸</label>
                        <input type="range" class="form-range" id="radarSize" name="Display.RadarSize" 
                               min="200" max="800" step="50" value="@(Model?.Display?.RadarSize ?? 400)">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">200px</small>
                            <span id="radarSizeValue" class="text-success">@(Model?.Display?.RadarSize ?? 400)px</span>
                            <small class="text-muted">800px</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="opacity" class="form-label">透明度</label>
                        <input type="range" class="form-range" id="opacity" name="Display.Opacity" 
                               min="0.1" max="1.0" step="0.1" value="@(Model?.Display?.Opacity ?? 0.9)">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">10%</small>
                            <span id="opacityValue" class="text-success">@((Model?.Display?.Opacity ?? 0.9) * 100)%</span>
                            <small class="text-muted">100%</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="theme" class="form-label">主题</label>
                        <select class="form-select" id="theme" name="Display.Theme">
                            <option value="dark" selected="@(Model?.Display?.Theme == "dark")">暗色主题</option>
                            <option value="light" selected="@(Model?.Display?.Theme == "light")">亮色主题</option>
                            <option value="green" selected="@(Model?.Display?.Theme == "green")">绿色主题</option>
                        </select>
                    </div>

                    <div class="row g-3">
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showPlayerNames" 
                                       name="Display.ShowPlayerNames" value="true" 
                                       checked="@(Model?.Display?.ShowPlayerNames ?? true)">
                                <label class="form-check-label" for="showPlayerNames">显示玩家名称</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showPlayerHealth" 
                                       name="Display.ShowPlayerHealth" value="true" 
                                       checked="@(Model?.Display?.ShowPlayerHealth ?? true)">
                                <label class="form-check-label" for="showPlayerHealth">显示生命值</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showDistances" 
                                       name="Display.ShowDistances" value="true" 
                                       checked="@(Model?.Display?.ShowDistances ?? false)">
                                <label class="form-check-label" for="showDistances">显示距离</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showGrid" 
                                       name="Display.ShowGrid" value="true" 
                                       checked="@(Model?.Display?.ShowGrid ?? true)">
                                <label class="form-check-label" for="showGrid">显示网格</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 过滤设置 -->
            <div class="col-lg-6">
                <div class="settings-section">
                    <h5><i class="fas fa-filter text-warning"></i> 过滤设置</h5>
                    
                    <div class="mb-3">
                        <label for="maxDisplayDistance" class="form-label">最大显示距离</label>
                        <input type="range" class="form-range" id="maxDisplayDistance" name="Filters.MaxDisplayDistance" 
                               min="100" max="1000" step="50" value="@(Model?.Filters?.MaxDisplayDistance ?? 500)">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">100m</small>
                            <span id="maxDistanceValue" class="text-success">@(Model?.Filters?.MaxDisplayDistance ?? 500)m</span>
                            <small class="text-muted">1000m</small>
                        </div>
                    </div>

                    <div class="row g-3">
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showTeammates" 
                                       name="Filters.ShowTeammates" value="true" 
                                       checked="@(Model?.Filters?.ShowTeammates ?? true)">
                                <label class="form-check-label text-primary" for="showTeammates">显示队友</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showEnemies" 
                                       name="Filters.ShowEnemies" value="true" 
                                       checked="@(Model?.Filters?.ShowEnemies ?? true)">
                                <label class="form-check-label text-danger" for="showEnemies">显示敌人</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showWeapons" 
                                       name="Filters.ShowWeapons" value="true" 
                                       checked="@(Model?.Filters?.ShowWeapons ?? true)">
                                <label class="form-check-label text-warning" for="showWeapons">显示武器</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showAmmo" 
                                       name="Filters.ShowAmmo" value="true" 
                                       checked="@(Model?.Filters?.ShowAmmo ?? false)">
                                <label class="form-check-label text-info" for="showAmmo">显示弹药</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showMedical" 
                                       name="Filters.ShowMedical" value="true" 
                                       checked="@(Model?.Filters?.ShowMedical ?? true)">
                                <label class="form-check-label text-success" for="showMedical">显示医疗</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showEquipment" 
                                       name="Filters.ShowEquipment" value="true" 
                                       checked="@(Model?.Filters?.ShowEquipment ?? false)">
                                <label class="form-check-label text-secondary" for="showEquipment">显示装备</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showVehicles" 
                                       name="Filters.ShowVehicles" value="true" 
                                       checked="@(Model?.Filters?.ShowVehicles ?? true)">
                                <label class="form-check-label text-warning" for="showVehicles">显示载具</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="showExtractionPoints" 
                                       name="Filters.ShowExtractionPoints" value="true" 
                                       checked="@(Model?.Filters?.ShowExtractionPoints ?? true)">
                                <label class="form-check-label text-light" for="showExtractionPoints">显示撤离点</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 更新设置 -->
            <div class="col-lg-6">
                <div class="settings-section">
                    <h5><i class="fas fa-sync text-info"></i> 更新设置</h5>
                    
                    <div class="mb-3">
                        <label for="refreshRate" class="form-label">刷新频率 (FPS)</label>
                        <input type="range" class="form-range" id="refreshRate" name="Updates.RefreshRate" 
                               min="30" max="120" step="10" value="@(Model?.Updates?.RefreshRate ?? 60)">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">30 FPS</small>
                            <span id="refreshRateValue" class="text-success">@(Model?.Updates?.RefreshRate ?? 60) FPS</span>
                            <small class="text-muted">120 FPS</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="dataRetentionTime" class="form-label">数据保留时间 (毫秒)</label>
                        <input type="range" class="form-range" id="dataRetentionTime" name="Updates.DataRetentionTime" 
                               min="1000" max="10000" step="500" value="@(Model?.Updates?.DataRetentionTime ?? 5000)">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">1秒</small>
                            <span id="dataRetentionValue" class="text-success">@((Model?.Updates?.DataRetentionTime ?? 5000) / 1000)秒</span>
                            <small class="text-muted">10秒</small>
                        </div>
                    </div>

                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="autoCleanup" 
                               name="Updates.AutoCleanup" value="true" 
                               checked="@(Model?.Updates?.AutoCleanup ?? true)">
                        <label class="form-check-label" for="autoCleanup">自动清理过期数据</label>
                    </div>
                </div>
            </div>

            <!-- 地图设置 -->
            <div class="col-lg-6">
                <div class="settings-section">
                    <h5><i class="fas fa-map text-success"></i> 地图设置</h5>
                    
                    <div class="mb-3">
                        <label for="defaultZoom" class="form-label">默认缩放</label>
                        <input type="range" class="form-range" id="defaultZoom" name="Map.DefaultZoom" 
                               min="0.5" max="3.0" step="0.1" value="@(Model?.Map?.DefaultZoom ?? 1.0)">
                        <div class="d-flex justify-content-between">
                            <small class="text-muted">50%</small>
                            <span id="defaultZoomValue" class="text-success">@((Model?.Map?.DefaultZoom ?? 1.0) * 100)%</span>
                            <small class="text-muted">300%</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="defaultMap" class="form-label">默认地图</label>
                        <select class="form-select" id="defaultMap" name="Map.DefaultMap">
                            <option value="default" selected="@(Model?.Map?.DefaultMap == "default")">默认地图</option>
                            <option value="urban" selected="@(Model?.Map?.DefaultMap == "urban")">城市地图</option>
                            <option value="desert" selected="@(Model?.Map?.DefaultMap == "desert")">沙漠地图</option>
                            <option value="forest" selected="@(Model?.Map?.DefaultMap == "forest")">森林地图</option>
                        </select>
                    </div>

                    <div class="row g-3">
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enablePanning" 
                                       name="Map.EnablePanning" value="true" 
                                       checked="@(Model?.Map?.EnablePanning ?? true)">
                                <label class="form-check-label" for="enablePanning">启用拖拽</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="centerOnPlayer" 
                                       name="Map.CenterOnPlayer" value="true" 
                                       checked="@(Model?.Map?.CenterOnPlayer ?? true)">
                                <label class="form-check-label" for="centerOnPlayer">以玩家为中心</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 保存按钮 -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <button type="submit" class="btn btn-success btn-lg me-3">
                    <i class="fas fa-save"></i> 保存设置
                </button>
                <button type="button" class="btn btn-outline-secondary btn-lg" onclick="previewSettings()">
                    <i class="fas fa-eye"></i> 预览效果
                </button>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    <script>
        // 实时更新滑块值显示
        document.getElementById('radarSize').addEventListener('input', function() {
            document.getElementById('radarSizeValue').textContent = this.value + 'px';
        });

        document.getElementById('opacity').addEventListener('input', function() {
            document.getElementById('opacityValue').textContent = Math.round(this.value * 100) + '%';
        });

        document.getElementById('maxDisplayDistance').addEventListener('input', function() {
            document.getElementById('maxDistanceValue').textContent = this.value + 'm';
        });

        document.getElementById('refreshRate').addEventListener('input', function() {
            document.getElementById('refreshRateValue').textContent = this.value + ' FPS';
        });

        document.getElementById('dataRetentionTime').addEventListener('input', function() {
            document.getElementById('dataRetentionValue').textContent = (this.value / 1000) + '秒';
        });

        document.getElementById('defaultZoom').addEventListener('input', function() {
            document.getElementById('defaultZoomValue').textContent = Math.round(this.value * 100) + '%';
        });

        // 重置为默认值
        async function resetToDefaults() {
            if (confirm('确定要重置所有设置为默认值吗？')) {
                try {
                    const response = await fetch('/api/config/reset', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        alert('设置已重置为默认值');
                        location.reload();
                    } else {
                        alert('重置设置失败');
                    }
                } catch (error) {
                    console.error('重置设置失败:', error);
                    alert('重置设置失败: ' + error.message);
                }
            }
        }

        // 预览设置效果
        function previewSettings() {
            // 收集当前设置
            const formData = new FormData(document.getElementById('settings-form'));
            const settings = {};
            
            for (let [key, value] of formData.entries()) {
                settings[key] = value;
            }
            
            // 在新窗口中打开雷达预览
            const previewWindow = window.open('@Url.Action("Index", "Radar")', 'preview', 'width=800,height=600');
            
            // 等待窗口加载后应用设置
            setTimeout(() => {
                if (previewWindow && previewWindow.radarManager) {
                    // 应用预览设置
                    console.log('应用预览设置:', settings);
                }
            }, 2000);
        }

        // 表单提交处理
        document.getElementById('settings-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const settings = {};
            
            // 构建设置对象
            settings.display = {
                radarSize: parseInt(formData.get('Display.RadarSize')),
                opacity: parseFloat(formData.get('Display.Opacity')),
                showPlayerNames: formData.has('Display.ShowPlayerNames'),
                showPlayerHealth: formData.has('Display.ShowPlayerHealth'),
                showDistances: formData.has('Display.ShowDistances'),
                showGrid: formData.has('Display.ShowGrid'),
                theme: formData.get('Display.Theme')
            };
            
            settings.filters = {
                showTeammates: formData.has('Filters.ShowTeammates'),
                showEnemies: formData.has('Filters.ShowEnemies'),
                showWeapons: formData.has('Filters.ShowWeapons'),
                showAmmo: formData.has('Filters.ShowAmmo'),
                showMedical: formData.has('Filters.ShowMedical'),
                showEquipment: formData.has('Filters.ShowEquipment'),
                showVehicles: formData.has('Filters.ShowVehicles'),
                showExtractionPoints: formData.has('Filters.ShowExtractionPoints'),
                showHighValueItems: formData.has('Filters.ShowHighValueItems'),
                maxDisplayDistance: parseFloat(formData.get('Filters.MaxDisplayDistance'))
            };
            
            settings.updates = {
                refreshRate: parseInt(formData.get('Updates.RefreshRate')),
                dataRetentionTime: parseInt(formData.get('Updates.DataRetentionTime')),
                autoCleanup: formData.has('Updates.AutoCleanup')
            };
            
            settings.map = {
                defaultZoom: parseFloat(formData.get('Map.DefaultZoom')),
                minZoom: 0.5,
                maxZoom: 3.0,
                enablePanning: formData.has('Map.EnablePanning'),
                centerOnPlayer: formData.has('Map.CenterOnPlayer'),
                defaultMap: formData.get('Map.DefaultMap')
            };
            
            // 发送设置到服务器
            fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(settings)
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert('设置保存成功！');
                } else {
                    alert('设置保存失败');
                }
            })
            .catch(error => {
                console.error('保存设置失败:', error);
                alert('保存设置失败: ' + error.message);
            });
        });
    </script>
}
