namespace DeltaForce_Radar.Models
{
    /// <summary>
    /// 雷达配置模型
    /// </summary>
    public class RadarConfig
    {
        public DisplaySettings Display { get; set; } = new DisplaySettings();
        public FilterSettings Filters { get; set; } = new FilterSettings();
        public UpdateSettings Updates { get; set; } = new UpdateSettings();
        public MapSettings Map { get; set; } = new MapSettings();
    }

    /// <summary>
    /// 显示设置
    /// </summary>
    public class DisplaySettings
    {
        public int RadarSize { get; set; } = 400; // 雷达大小（像素）
        public float Opacity { get; set; } = 0.9f; // 透明度
        public bool ShowPlayerNames { get; set; } = true;
        public bool ShowPlayerHealth { get; set; } = true;
        public bool ShowDistances { get; set; } = false;
        public bool ShowGrid { get; set; } = true;
        public string Theme { get; set; } = "dark"; // 主题：dark/light
    }

    /// <summary>
    /// 过滤设置
    /// </summary>
    public class FilterSettings
    {
        public bool ShowTeammates { get; set; } = true;
        public bool ShowEnemies { get; set; } = true;
        public bool ShowWeapons { get; set; } = true;
        public bool ShowAmmo { get; set; } = false;
        public bool ShowMedical { get; set; } = true;
        public bool ShowEquipment { get; set; } = false;
        public bool ShowVehicles { get; set; } = true;
        public bool ShowExtractionPoints { get; set; } = true;
        public bool ShowHighValueItems { get; set; } = true;
        public float MaxDisplayDistance { get; set; } = 500f; // 最大显示距离
    }

    /// <summary>
    /// 更新设置
    /// </summary>
    public class UpdateSettings
    {
        public int RefreshRate { get; set; } = 60; // 刷新率（毫秒）
        public int DataRetentionTime { get; set; } = 5000; // 数据保留时间（毫秒）
        public bool AutoCleanup { get; set; } = true; // 自动清理过期数据
    }

    /// <summary>
    /// 地图设置
    /// </summary>
    public class MapSettings
    {
        public float DefaultZoom { get; set; } = 1.0f;
        public float MinZoom { get; set; } = 0.5f;
        public float MaxZoom { get; set; } = 3.0f;
        public bool EnablePanning { get; set; } = true;
        public bool CenterOnPlayer { get; set; } = true;
        public string DefaultMap { get; set; } = "default";
    }

    /// <summary>
    /// 颜色配置
    /// </summary>
    public static class ColorConfig
    {
        public static readonly Dictionary<PlayerType, string> PlayerColors = new()
        {
            { PlayerType.Self, "#00FF00" },      // 绿色
            { PlayerType.Teammate, "#0080FF" },  // 蓝色
            { PlayerType.Enemy, "#FF0000" }      // 红色
        };

        public static readonly Dictionary<ItemType, string> ItemColors = new()
        {
            { ItemType.Weapon, "#FFD700" },         // 金色
            { ItemType.Ammo, "#FFA500" },           // 橙色
            { ItemType.Medical, "#FF69B4" },        // 粉色
            { ItemType.Equipment, "#9370DB" },      // 紫色
            { ItemType.Vehicle, "#32CD32" },        // 绿色
            { ItemType.ExtractionPoint, "#FF1493" }, // 深粉色
            { ItemType.HighValue, "#FFD700" }       // 金色
        };
    }
}
